# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env.local
.env*.local

# 注意：.env.development, .env.testing, .env.staging, .env.production 可以提交
# 但不应包含敏感信息，敏感信息应通过部署平台的环境变量设置

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.history
