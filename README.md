# Lumii Official Website

> 🚀 现代化企业官方网站，创新驱动数字化未来

## 📋 项目简介

Lumii Official Website 是一个现代化的企业官方网站，采用最新前端技术栈构建，为企业提供专业、时尚、高性能的数字化展示平台。项目注重用户体验、性能优化和可维护性，是企业数字化转型的理想选择。

## ✨ 核心特性

- 🎨 **现代化设计** - 简洁优雅的界面，支持响应式布局
- ⚡ **高性能** - 基于 Next.js 的 SSR/SSG，极速加载体验
- 🎭 **流畅动画** - Motion 动画库打造丝滑交互效果
- 📱 **移动优先** - 完美适配各种设备和屏幕尺寸
- 🔧 **开发友好** - TypeScript + 现代工具链，提升开发效率

## 🛠️ 技术栈

- **框架**: Next.js 15.4.3 + React 19.1.0
- **语言**: TypeScript 5.x
- **样式**: Tailwind CSS 4.x
- **动画**: Motion (Framer Motion)
- **工具**: ESLint + Prettier
- **图标**: Iconfont 字体图标库

> 📖 详细的技术架构说明请参考 [ARCHITECTURE.md](docs/ARCHITECTURE.md)

## 🎨 组件使用指南

### Icon 图标组件

项目使用自定义的 Iconfont 图标库，提供了丰富的图标资源和类型安全的使用方式。

#### 基本使用

```tsx
import Icon from '@/components/ui/Icon';

// 基础用法
<Icon name="weixin" />

// 设置尺寸
<Icon name="bofang" size="lg" />

// 自定义样式
<Icon name="douyin" size="2xl" className="text-blue-500" />

// 添加点击事件
<Icon
  name="guanbi"
  size="base"
  onClick={() => console.log('关闭')}
  className="cursor-pointer hover:text-red-500"
/>
```

#### 可用图标

项目包含以下图标（支持 TypeScript 智能提示）：

| 图标名称 | 描述 | 图标名称 | 描述 |
|---------|------|---------|------|
| `weixin` | 微信 | `douyin` | 抖音 |
| `xiaohongshu` | 小红书 | `weibo` | 微博 |
| `bofang` | 播放 | `zanting` | 暂停 |
| `wancheng` | 完成 | `guanbi` | 关闭 |
| `caidan` | 菜单 | `xiala` | 下拉 |
| `dianhua` | 电话 | `shijian` | 时间 |
| `weizhi` | 位置 | `duibi` | 对比 |
| `shouqi` | 收起 | `qiehuan` | 切换 |
| `shangchuanzhaopian` | 上传照片 | `xiaochengxu` | 小程序 |
| `yijianmeiyan1` | 一键美颜 | `yachimeibai` | 牙齿美白 |

#### 尺寸规格

| 尺寸 | 说明 | CSS 类名 |
|------|------|----------|
| `xs` | 超小 | `iconfont-xs` |
| `sm` | 小 | `iconfont-sm` |
| `base` | 基础（默认） | `iconfont-base` |
| `lg` | 大 | `iconfont-lg` |
| `xl` | 超大 | `iconfont-xl` |
| `2xl` | 2倍大 | `iconfont-2xl` |
| `3xl` | 3倍大 | `iconfont-3xl` |
| `4xl` | 4倍大 | `iconfont-4xl` |

#### 组件属性

```tsx
interface IconProps {
  /** 图标名称 */
  name: IconName;
  /** 图标尺寸 */
  size?: IconSize;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 点击事件 */
  onClick?: () => void;
}
```

#### 使用示例

```tsx
// 社交媒体图标组
<div className="flex space-x-4">
  <Icon name="weixin" size="lg" className="text-green-500" />
  <Icon name="douyin" size="lg" className="text-black" />
  <Icon name="xiaohongshu" size="lg" className="text-red-500" />
  <Icon name="weibo" size="lg" className="text-orange-500" />
</div>

// 播放控制按钮
<div className="flex items-center space-x-2">
  <Icon
    name="bofang"
    size="xl"
    onClick={() => handlePlay()}
    className="text-blue-600 hover:text-blue-800 cursor-pointer"
  />
  <Icon
    name="zanting"
    size="xl"
    onClick={() => handlePause()}
    className="text-gray-600 hover:text-gray-800 cursor-pointer"
  />
</div>

// 功能图标
<Icon
  name="shangchuanzhaopian"
  size="2xl"
  className="text-purple-500"
/>
```

#### 注意事项

- 所有图标名称都有 TypeScript 类型检查，确保使用正确的图标名称
- 图标支持所有 Tailwind CSS 的文本颜色类
- 可以通过 `className` 添加 hover 效果和过渡动画
- 带有 `onClick` 事件的图标会自动添加键盘访问性支持

## 📁 项目结构

```
lumii-official/
├── src/
│   ├── app/              # Next.js App Router
│   ├── components/       # React 组件
│   ├── lib/             # 工具函数
│   └── types/           # 类型定义
├── docs/                # 项目文档
└── public/              # 静态资源
```

## 🚀 快速开始

### 环境要求
- Node.js 18.0+
- npm 9.0+

### 安装和运行
```bash
# 克隆项目
git clone <repository-url>
cd lumii-official

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站效果。

### 常用命令
```bash
npm run build     # 构建生产版本
npm run start     # 启动生产服务器
npm run lint      # 代码检查
npm run format    # 代码格式化
```

> 🔧 完整的开发指南请参考 [DEVELOPMENT.md](docs/DEVELOPMENT.md)

## 📖 文档导航

- **[技术架构](docs/ARCHITECTURE.md)** - 详细的系统架构和技术选型说明
- **[开发指南](docs/DEVELOPMENT.md)** - 完整的开发环境配置和工作流程
- **[环境配置](docs/ENVIRONMENT.md)** - 多环境配置管理系统使用指南
- **[API 文档](#)** - 接口文档（待完善）
- **[部署指南](#)** - 生产环境部署说明（待完善）

## 🌟 项目亮点

### 技术创新
- 采用 Next.js 15 最新 App Router 架构
- 集成 React 19 最新特性
- 使用 Tailwind CSS 4.x 最新版本

### 用户体验
- 响应式设计，完美适配所有设备
- 流畅的页面切换和交互动画
- 优化的加载性能和 SEO 支持

### 开发体验
- 完整的 TypeScript 类型安全
- 现代化的开发工具链
- 组件化架构，易于维护和扩展

## 🤝 参与贡献

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork** 项目到您的 GitHub 账户
2. **创建分支** `git checkout -b feature/amazing-feature`
3. **提交更改** `git commit -m 'Add amazing feature'`
4. **推送分支** `git push origin feature/amazing-feature`
5. **创建 Pull Request**

### 贡献指南
- 遵循现有的代码风格和规范
- 添加适当的测试用例
- 更新相关文档
- 确保所有检查通过

## 📄 许可证

本项目采用 MIT 许可证 - 详情请查看 [LICENSE](LICENSE) 文件。

## 📞 联系方式

- **项目仓库**: [GitHub](https://github.com/lumii/lumii-official)
- **问题反馈**: [Issues](https://github.com/lumii/lumii-official/issues)
- **官方网站**: [https://lumii.com](https://lumii.com)
- **邮箱**: <EMAIL>

---

<div align="center">

**Lumii** - 创新驱动数字化未来 🚀

Made with ❤️ by Lumii Team

</div>
