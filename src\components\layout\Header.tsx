'use client'

import { motion } from 'motion/react'
import Link from 'next/link'
import { useState, useCallback, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import Image from 'next/image'
import Icon from '@/components/ui/Icon'
import LanguageSwitcher from '@/components/ui/LanguageSwitcher'
import { useTranslation } from '@/lib/i18n/client'
import { useHeaderHeight } from '@/lib/contexts/HeaderHeightContext'
import type { IHeader, ISite } from '@/types/strapi'

import Logo from '/public/images/logo.png'
import BeatyCn from '/public/images/beautyCn.png'
import BeatyEn from '/public/images/beautyEn.png'
import { isMiniProgramEnvironment } from '@/lib/utils/miniprogram'

interface HeaderProps {
  data?: IHeader | null
  siteData?: ISite | null
  ref?: React.Ref<HTMLElement>
}

export default function Header({ data, siteData, ref }: HeaderProps) {
  const { locale } = useTranslation()
  const { headerRef } = useHeaderHeight()
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [menus] = useState(data?.menus || [])
  const [site] = useState(siteData || null)
  const [activeMenu, setActiveMenu] = useState<number | null>(null)

  // 抽取公共的AI变美按钮渲染逻辑
  const renderAIBeautyContent = (
    item: { url: string | null; label: string },
    height: number = 20
  ) => {
    if (item.url !== 'image-beautify') {
      return item.label
    }

    const imageSource = locale === 'zh-CN' ? BeatyCn : BeatyEn

    return (
      <div
        className="shimmer-mask-rainbow"
        style={{
          WebkitMaskImage: `url(${imageSource.src})`,
          maskImage: `url(${imageSource.src})`,
          maskSize: 'contain',
          maskPosition: 'center',
          maskRepeat: 'no-repeat',
        }}
      >
        <Image src={imageSource} alt={item.label} height={height} />
      </div>
    )
  }

  // Combine refs
  const combinedRef = useCallback(
    (node: HTMLElement | null) => {
      headerRef(node)
      if (typeof ref === 'function') {
        ref(node)
      } else if (ref) {
        ref.current = node
      }
    },
    [headerRef, ref]
  )
  // PC端滚动隐藏/显示状态
  const [isHeaderVisible, setIsHeaderVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  // 背景色状态：true为黑色，false为透明
  const [hasScrolledBackground, setHasScrolledBackground] = useState(false)

  // 初始化背景状态和小程序环境检测
  useEffect(() => {
    const initializeBackground = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth < 1024) {
          // 移动端始终有背景
          setHasScrolledBackground(true)
        } else {
          // PC端根据滚动位置决定
          setHasScrolledBackground(window.scrollY > 10)
        }
      }
    }

    initializeBackground()
  }, [])

  // 根据当前路径设置活跃菜单
  useEffect(() => {
    if (menus.length === 0) return

    // 获取当前路径，去掉语言前缀
    const currentPath = pathname.replace(`/${locale}`, '') || '/'

    // 查找匹配的菜单项
    const matchedMenu = menus.find((menu) => {
      const menuPath = `/${menu.url}`
      return currentPath === menuPath || currentPath.startsWith(`${menuPath}/`)
    })

    if (matchedMenu) {
      setActiveMenu(matchedMenu.id)
    } else {
      // 如果没有匹配的菜单，默认选中首页
      const homeMenu = menus.find(
        (menu) =>
          menu.url === '' || menu.url === 'home' || menu.label === '首页' || menu.label === 'Home'
      )
      if (homeMenu) {
        setActiveMenu(homeMenu.id)
      } else if (menus.length > 0) {
        // 如果找不到首页，选中第一个菜单项
        setActiveMenu(menus[0].id)
      }
    }
  }, [pathname, locale, menus])

  // 监听滚动事件（仅PC端）
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY

      // 只在PC端应用滚动隐藏逻辑
      if (window.innerWidth >= 1024) {
        // lg breakpoint
        if (currentScrollY > lastScrollY && currentScrollY > 100) {
          // 向下滚动且超过100px时隐藏
          setIsHeaderVisible(false)
        } else if (currentScrollY < lastScrollY) {
          // 向上滚动时显示，并设置黑色背景
          setIsHeaderVisible(true)
          setHasScrolledBackground(true)
        }

        // 如果滚动到顶部，恢复透明背景
        if (currentScrollY <= 10) {
          setHasScrolledBackground(false)
        }
      } else {
        // 移动端始终显示，背景始终为黑色
        setIsHeaderVisible(true)
        setHasScrolledBackground(true)
      }

      setLastScrollY(currentScrollY)
    }

    // 监听窗口大小变化，确保移动端始终显示Header
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsHeaderVisible(true)
        setHasScrolledBackground(true) // 移动端始终有背景
      } else {
        // PC端根据当前滚动位置决定背景
        const currentScrollY = window.scrollY
        setHasScrolledBackground(currentScrollY > 10)
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleResize)
    }
  }, [lastScrollY])

  return (
    <motion.header
      ref={combinedRef}
      initial={{ y: -100 }}
      animate={{
        y: isHeaderVisible ? 0 : -100,
        backgroundColor: hasScrolledBackground ? 'rgba(0, 0, 0, 1)' : 'rgba(0, 0, 0, 0)',
      }}
      transition={{
        duration: 0.3,
        ease: 'easeInOut',
      }}
      className="fixed top-0 right-0 left-0 z-50"
      style={{
        backdropFilter: hasScrolledBackground ? 'blur(10px)' : 'none',
      }}
    >
      {/* Desktop Layout */}
      <div className="hidden lg:block">
        <div className="mx-auto w-[76%]">
          {/* Logo Section */}
          <div className="relative flex items-center justify-center py-4">
            <motion.div whileHover={{ scale: 1 }} className="flex-shrink-0">
              <Link href={`/${locale}`} className="block">
                <Image
                  src={site?.logo?.url || Logo}
                  alt="Lumii Logo"
                  width={120}
                  height={40}
                  className="h-8 w-auto"
                />
              </Link>
            </motion.div>
            {/* Language Switcher */}
            <div className="absolute top-4 right-[0px]">
              <LanguageSwitcher />
            </div>
          </div>

          {/* Navigation Section */}
          <div className="flex items-center justify-between">
            <nav className="flex flex-1 items-center justify-center pb-2">
              <div className={`flex ${locale === 'zh-CN' ? 'space-x-8' : 'space-x-2'} `}>
                {menus.map((item, index) => {
                  const isActive = activeMenu === item.id

                  return (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      className="flex-shrink-0"
                    >
                      <Link
                        href={`/${locale}/${item.url}`}
                        className={`relative flex items-center px-3 py-2 text-[14px] whitespace-nowrap text-white transition-all duration-200 hover:font-[600] ${isActive
                          ? 'font-semibold after:absolute after:bottom-0 after:left-1/2 after:h-0.5 after:w-8 after:-translate-x-1/2 after:transform after:bg-white after:content-[""]'
                          : 'font-light hover:font-semibold hover:after:absolute hover:after:bottom-0 hover:after:left-1/2 hover:after:h-0.5 hover:after:w-8 hover:after:-translate-x-1/2 hover:after:transform hover:after:bg-white hover:after:content-[""]'
                          }`}
                      >
                        {renderAIBeautyContent(item)}
                      </Link>
                    </motion.div>
                  )
                })}
              </div>
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      {
        isMiniProgramEnvironment() ? null : (
          <div className="lg:hidden">
            <div className="w-full px-4 pt-[34px] pb-5">
              <div className="flex items-center justify-between">
                {/* Logo */}
                <motion.div whileHover={{ scale: 1.05 }} className="flex-shrink-0">
                  <Link href={`/${locale}`} className="block">
                    <Image src={Logo} alt="Lumii Logo" width={120} height={40} className="h-8 w-auto" />
                  </Link>
                </motion.div>

                <div onClick={() => setIsMenuOpen(!isMenuOpen)} className="text-white">
                  {isMenuOpen ? (
                    <Icon name="guanbi" className="text-white" size="base" />
                  ) : (
                    <Icon name="caidan" className="text-white" size="base" />
                  )}
                </div>
              </div>
            </div>

            {/* Mobile Navigation */}
            {isMenuOpen && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                className="fixed inset-x-0 top-[86px] bottom-0 z-40 bg-black"
                style={{
                  height: 'calc(100vh - 86px)', // 减去header高度
                  minHeight: 'calc(100dvh - 86px)', // 使用动态视口高度兼容移动端
                }}
              >
                <div className="flex h-full flex-col justify-between overflow-y-auto">
                  {/* 菜单项区域 */}
                  <div className="flex-1 space-y-1 border-t border-gray-800 px-2 pt-2">
                    {menus.map((item) => {
                      const isActive = activeMenu === item.id
                      return (
                        <Link
                          key={item.id}
                          href={`/${locale}/${item.url}`}
                          className={`block border-b border-gray-800 px-3 py-4 text-sm text-white last:border-b-0 hover:text-gray-300 active:bg-gray-800 ${isActive ? 'bg-gray-800' : ''
                            }`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {renderAIBeautyContent(item)}
                        </Link>
                      )
                    })}
                  </div>

                  {/* 语言切换器区域 - 固定在底部 */}
                  <div className="pb-safe border-t border-gray-800 px-3 py-4">
                    <LanguageSwitcher />
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        )
      }
    </motion.header>
  )
}
