/**
 * 验证工具函数
 */

import { SUPPORTED_IMAGE_FORMATS, MAX_FILE_SIZE, type SupportedImageFormat } from '@/lib/constants/image'

// 验证结果类型
export interface ValidationResult {
  valid: boolean
  error?: string
  errorCode?: string
}

/**
 * 验证图片文件
 */
export function validateImageFile(file: File): ValidationResult {
  // 检查文件类型
  if (!SUPPORTED_IMAGE_FORMATS.includes(file.type as SupportedImageFormat)) {
    return {
      valid: false,
      error: '不支持的文件格式，请选择 JPG、PNG 或 WebP 格式的图片',
      errorCode: 'UNSUPPORTED_FORMAT'
    }
  }

  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    const maxSizeMB = Math.round(MAX_FILE_SIZE / (1024 * 1024))
    return {
      valid: false,
      error: `文件大小超出限制，最大支持 ${maxSizeMB}MB`,
      errorCode: 'FILE_TOO_LARGE'
    }
  }

  // 检查文件是否为空
  if (file.size === 0) {
    return {
      valid: false,
      error: '文件为空，请选择有效的图片文件',
      errorCode: 'EMPTY_FILE'
    }
  }

  return { valid: true }
}

/**
 * 验证处理强度参数
 */
export function validateIntensity(intensity: number): ValidationResult {
  if (typeof intensity !== 'number' || isNaN(intensity)) {
    return {
      valid: false,
      error: '强度参数必须是数字',
      errorCode: 'INVALID_INTENSITY_TYPE'
    }
  }

  if (intensity < 0 || intensity > 100) {
    return {
      valid: false,
      error: '强度参数必须在 0-100 之间',
      errorCode: 'INTENSITY_OUT_OF_RANGE'
    }
  }

  return { valid: true }
}

/**
 * 验证图片质量参数
 */
export function validateQuality(quality: number): ValidationResult {
  if (typeof quality !== 'number' || isNaN(quality)) {
    return {
      valid: false,
      error: '质量参数必须是数字',
      errorCode: 'INVALID_QUALITY_TYPE'
    }
  }

  if (quality < 1 || quality > 100) {
    return {
      valid: false,
      error: '质量参数必须在 1-100 之间',
      errorCode: 'QUALITY_OUT_OF_RANGE'
    }
  }

  return { valid: true }
}

/**
 * 验证 URL 格式
 */
export function validateUrl(url: string): ValidationResult {
  if (!url || typeof url !== 'string') {
    return {
      valid: false,
      error: 'URL 不能为空',
      errorCode: 'EMPTY_URL'
    }
  }

  try {
    new URL(url)
    return { valid: true }
  } catch {
    return {
      valid: false,
      error: 'URL 格式无效',
      errorCode: 'INVALID_URL'
    }
  }
}
