/**
 * 图片美化 Zustand Store
 */

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { processImage, mockProcessImage, downloadProcessedImage, uploadImage } from '@/lib/api/image-beautify'
import { validateImageFile } from '@/lib/utils/validation'
import { createImageFile, cleanupObjectURL, generateFileName, downloadFile, type ImageFile } from '@/lib/utils/file'
import { PROCESSING_STATUS, DEFAULT_PROCESSING_OPTIONS, type ProcessingStatus, type ProcessingType } from '@/lib/constants/image'
import type { ProcessingProgress, ProcessingOptions, ProcessingResponse } from '@/lib/api/types'

// Store 状态接口
interface ImageBeautifyState {
  // 状态数据
  originalImage: ImageFile | null
  processedImage: ImageFile | null
  status: ProcessingStatus
  error: string | null
  progress: ProcessingProgress | null

  // 处理历史
  appliedProcessingTypes: Set<ProcessingType>

  // 配置
  useMockApi: boolean
  
  // 同步 Actions
  setOriginalImage: (image: ImageFile | null) => void
  setProcessedImage: (image: ImageFile | null) => void
  setStatus: (status: ProcessingStatus) => void
  setError: (error: string | null) => void
  setProgress: (progress: ProcessingProgress | null) => void
  setUseMockApi: (useMock: boolean) => void
  addAppliedProcessingType: (type: ProcessingType) => void
  reset: () => void
  
  // 异步 Actions
  uploadImageFile: (file: File) => Promise<void>
  processImageWithType: (type: ProcessingType, options?: Partial<ProcessingOptions>) => Promise<void>
  downloadProcessedImageFile: (translations?: { saveImageTip: string; close: string }) => Promise<void>
  
  // 便捷方法（保留一些常用的）
  teethWhitening: (intensity?: number) => Promise<void>
  beautyEnhancement: (intensity?: number) => Promise<void>
}

// 初始状态
const initialState = {
  originalImage: null,
  processedImage: null,
  status: PROCESSING_STATUS.IDLE as ProcessingStatus,
  error: null,
  progress: null,
  appliedProcessingTypes: new Set<ProcessingType>(),
  useMockApi: false, // 使用真实 API
}

// 创建 Store
export const useImageBeautifyStore = create<ImageBeautifyState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      ...initialState,
      
      // 同步 Actions
      setOriginalImage: (image) => set({ originalImage: image }),
      setProcessedImage: (image) => set({ processedImage: image }),
      setStatus: (status) => set({ status, error: null }),
      setError: (error) => set({ error, status: PROCESSING_STATUS.ERROR }),
      setProgress: (progress) => set({ progress }),
      setUseMockApi: (useMockApi) => set({ useMockApi }),
      addAppliedProcessingType: (type) => set((state) => ({
        appliedProcessingTypes: new Set([...state.appliedProcessingTypes, type])
      })),
      
      reset: () => {
        const { originalImage, processedImage } = get()
        
        // 清理对象 URL
        if (originalImage) {
          cleanupObjectURL(originalImage.url)
        }
        if (processedImage) {
          cleanupObjectURL(processedImage.url)
        }
        
        set(initialState)
      },
      
      // 异步 Actions
      uploadImageFile: async (file: File) => {
        try {
          set({ status: PROCESSING_STATUS.UPLOADING, error: null })

          // 验证文件
          const validation = validateImageFile(file)
          if (!validation.valid) {
            throw new Error(validation.error || 'Invalid file')
          }

          // 创建图片文件对象，使用本地URL
          const imageFile = await createImageFile(file)

          // 清理之前的处理结果
          const { processedImage } = get()
          if (processedImage) {
            cleanupObjectURL(processedImage.url)
            set({ processedImage: null })
          }

          set({
            originalImage: imageFile,
            status: PROCESSING_STATUS.IDLE,
            appliedProcessingTypes: new Set() // 重置处理历史
          })

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Upload failed'
          set({
            error: errorMessage,
            status: PROCESSING_STATUS.ERROR
          })
          throw error
        }
      },
      
      processImageWithType: async (type: ProcessingType, options: Partial<ProcessingOptions> = {}) => {
        const { originalImage, processedImage: currentProcessedImage, useMockApi } = get()

        if (!originalImage) {
          throw new Error('No image to process')
        }

        try {
          set({ status: PROCESSING_STATUS.PROCESSING, error: null })

          const processingOptions: ProcessingOptions = {
            type,
            quality: options.quality || DEFAULT_PROCESSING_OPTIONS.quality
          }

          // 进度回调
          const handleProgress = (progress: ProcessingProgress) => {
            set({ progress })
          }

          // 确定要处理的图片：如果有处理结果，使用处理结果；否则使用原图
          const imageToProcess = currentProcessedImage || originalImage

          let processedData: ProcessingResponse | null = null
          if (useMockApi) {
            // 使用模拟 API
            const result = await mockProcessImage(imageToProcess.url, processingOptions, handleProgress)
            if (!result.success || !result.data) {
              set({
                status: PROCESSING_STATUS.ERROR,
                progress: null
              })
              return
            }
            processedData = result.data
          } else {
            // 使用真实 API - 先上传到OSS，再处理
            handleProgress({
              stage: 'analyzing',
              progress: 5,
              message: 'Uploading image to OSS...'
            })

            // 上传要处理的图片到OSS（可能是原图或上次处理的结果）
            const ossImageUrl = await uploadImage(imageToProcess.file)
            if (!ossImageUrl) {
              set({
                status: PROCESSING_STATUS.ERROR,
                progress: null
              })
              return
            }

            handleProgress({
              stage: 'analyzing',
              progress: 15,
              message: 'Image uploaded, starting processing...'
            })

            // 使用OSS URL进行处理
            processedData = await processImage(ossImageUrl, processingOptions, handleProgress)
            if (!processedData) {
              set({
                status: PROCESSING_STATUS.ERROR,
                progress: null
              })
              return
            }
          }

          // 确保processedData不为null
          if (!processedData) {
            set({
              status: PROCESSING_STATUS.ERROR,
              progress: null
            })
            return
          }

          // 创建处理后的图片文件对象
          let processedFile: File
          let processedUrl: string
          let originalApiUrl: string | undefined

          if (processedData.processedImageBlob) {
            // 如果返回了 Blob
            const fileName = generateFileName(imageToProcess.file.name, type)
            processedFile = new File([processedData.processedImageBlob], fileName, {
              type: imageToProcess.file.type
            })
            processedUrl = URL.createObjectURL(processedFile)
            // Blob 情况下没有原始 URL
            originalApiUrl = undefined
          } else if (processedData.processedImageUrl) {
            // 如果返回了 URL，需要下载
            originalApiUrl = processedData.processedImageUrl // 保存原始 API URL
            const blob = await downloadProcessedImage(processedData.processedImageUrl)
            if (!blob) {
              set({
                status: PROCESSING_STATUS.ERROR,
                progress: null
              })
              return
            }
            const fileName = generateFileName(imageToProcess.file.name, type)
            processedFile = new File([blob], fileName, {
              type: imageToProcess.file.type
            })
            processedUrl = URL.createObjectURL(processedFile)
          } else {
            set({
              status: PROCESSING_STATUS.ERROR,
              progress: null
            })
            return
          }

          const processedImageFile: ImageFile = {
            file: processedFile,
            url: processedUrl,
            originalUrl: originalApiUrl, // 保存原始 API URL
            width: imageToProcess.width,
            height: imageToProcess.height,
            size: processedFile.size,
            format: imageToProcess.format
          }
          
          // 清理之前的处理结果
          if (currentProcessedImage) {
            cleanupObjectURL(currentProcessedImage.url)
          }
          
          set({
            processedImage: processedImageFile,
            status: PROCESSING_STATUS.COMPLETED,
            progress: null
          })

          // 记录处理历史，每种效果只能使用一次，但支持相互叠加
          const { addAppliedProcessingType } = get()
          addAppliedProcessingType(type)
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Processing failed'
          set({ 
            error: errorMessage, 
            status: PROCESSING_STATUS.ERROR,
            progress: null
          })
          throw error
        }
      },
      
      downloadProcessedImageFile: async (translations?: { saveImageTip: string; close: string }) => {
        const { processedImage } = get()

        if (!processedImage) {
          throw new Error('No processed image to download')
        }

        try {
          set({ error: null })
          await downloadFile(processedImage.file, processedImage.file.name, translations)
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Download failed'
          set({ error: errorMessage })
          throw error
        }
      },
      
      // 便捷方法
      teethWhitening: async () => {
        const { processImageWithType } = get()
        await processImageWithType('teeth-whitening')
      },
      
      beautyEnhancement: async () => {
        const { processImageWithType } = get()
        await processImageWithType('beauty-enhancement')
      },
    }),
    {
      name: 'image-beautify-store',
    }
  )
)

// 选择器 Hooks（用于性能优化）
export const useOriginalImage = () => useImageBeautifyStore(state => state.originalImage)
export const useProcessedImage = () => useImageBeautifyStore(state => state.processedImage)
export const useImageBeautifyStatus = () => useImageBeautifyStore(state => state.status)
export const useImageBeautifyError = () => useImageBeautifyStore(state => state.error)
export const useImageBeautifyProgress = () => useImageBeautifyStore(state => state.progress)
export const useHasImage = () => useImageBeautifyStore(state => !!state.originalImage)
export const useHasProcessedImage = () => useImageBeautifyStore(state => !!state.processedImage)
export const useAppliedProcessingTypes = () => useImageBeautifyStore(state => state.appliedProcessingTypes)
export const useIsProcessingTypeApplied = (type: ProcessingType) => useImageBeautifyStore(state =>
  state.appliedProcessingTypes.has(type)
)
export const useCanProcess = () => useImageBeautifyStore(state =>
  !!state.originalImage && state.status === 'idle'
)


