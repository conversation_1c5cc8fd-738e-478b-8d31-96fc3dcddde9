import { getDictionary } from '@/lib/i18n/dictionaries'
import { getAboutUsPage } from '@/lib/cms/about'
import { transformAboutPageToDictionary } from '@/lib/cms/transform'
import AboutContent from './AboutContent'
import type { Metadata } from 'next'
import type { Locale } from '@/lib/i18n/config'

interface AboutPageProps {
  params: Promise<{ locale: Locale }>
}

export async function generateMetadata({ params }: AboutPageProps): Promise<Metadata> {
  const { locale } = await params

  // 尝试从CMS获取数据
  const cmsData = await getAboutUsPage(locale)

  if (cmsData) {
    return {
      title: cmsData.title,
      description: cmsData.blocks[0]?.description || cmsData.title,
    }
  }

  // 回退到静态翻译
  const dictionary = await getDictionary(locale)
  return {
    title: dictionary.about.title,
    description: dictionary.about.hero.description,
  }
}

export default async function AboutPage({ params }: AboutPageProps) {
  const { locale } = await params

  // 获取静态翻译作为基础和回退
  const dictionary = await getDictionary(locale)

  // 尝试从CMS获取数据
  const cmsData = await getAboutUsPage(locale)

  if (cmsData) {
    // 将CMS数据转换为Dictionary格式
    const transformedDictionary = transformAboutPageToDictionary(cmsData, dictionary)
    return <AboutContent dictionary={transformedDictionary} heroes={cmsData.heroes} />
  }

  // 回退到静态翻译
  return <AboutContent dictionary={dictionary} />
}
