import type { Locale } from './config'
import type { Dictionary } from './types'

const dictionaries = {
  'zh-CN': () => import('./dictionaries/zh-CN.json').then((module) => module.default),
  'en-US': () => import('./dictionaries/en-US.json').then((module) => module.default),
} as const

export const getDictionary = async (locale: Locale): Promise<Dictionary> => {
  const dictionaryLoader = dictionaries[locale]
  if (!dictionaryLoader) {
    throw new Error(`Dictionary for locale "${locale}" not found`)
  }
  return dictionaryLoader()
}


