import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { i18n } from './src/lib/i18n/config'

function getLocale(request: NextRequest): string {
  // Check if there is any supported locale in the pathname
  const pathname = request.nextUrl.pathname
  const pathnameIsMissingLocale = i18n.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  )

  // Redirect if there is no locale
  if (pathnameIsMissingLocale) {
    // Try to get locale from Accept-Language header
    const acceptLanguage = request.headers.get('accept-language')
    let locale: string = i18n.defaultLocale

    if (acceptLanguage) {
      // Simple locale detection based on Accept-Language header
      for (const supportedLocale of i18n.locales) {
        if (acceptLanguage.includes(supportedLocale.toLowerCase()) ||
            acceptLanguage.includes(supportedLocale.split('-')[0])) {
          locale = supportedLocale
          break
        }
      }
    }

    return locale
  }

  // Extract locale from pathname
  const segments = pathname.split('/')
  const localeFromPath = segments[1]
  
  if (i18n.locales.includes(localeFromPath as typeof i18n.defaultLocale)) {
    return localeFromPath as typeof i18n.defaultLocale
  }

  return i18n.defaultLocale
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // Check if the pathname is missing a locale
  const pathnameIsMissingLocale = i18n.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  )

  // Redirect if there is no locale
  if (pathnameIsMissingLocale) {
    const locale = getLocale(request)
    
    // Redirect to the same path with locale prefix
    return NextResponse.redirect(
      new URL(`/${locale}${pathname.startsWith('/') ? '' : '/'}${pathname}`, request.url)
    )
  }

  // Add locale and pathname to request headers for use in server components
  const response = NextResponse.next()
  const locale = getLocale(request)
  response.headers.set('x-locale', locale)
  response.headers.set('x-pathname', request.nextUrl.pathname)

  return response
}

export const config = {
  // Matcher ignoring `/_next/` and `/api/`
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|.*\\..*).*)']
}
