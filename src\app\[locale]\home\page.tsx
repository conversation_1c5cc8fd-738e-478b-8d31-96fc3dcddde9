/**
 * 首页 - 服务端组件（仅负责数据获取）
 */
import { getPage } from '@/lib/cms/page'
import type { Metadata } from 'next'
import HomeContent from './HomeContent'

interface HomePageProps {
  params: Promise<{ locale: string }>
}

// 生成页面元数据
export async function generateMetadata({ params }: HomePageProps): Promise<Metadata> {
  const { locale } = await params
  const pageData = await getPage(locale, 'home')
  if (pageData?.title) {
    return {
      title: pageData.title,
      description: pageData.heroes[0]?.description || pageData.title,
    }
  }

  // 回退到默认标题
  return {
    title: '首页',
    description: '探索 Lumii 的尊享之旅',
  }
}

// 服务端组件 - 只负责数据获取
export default async function Home({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const pageData = await getPage(locale, 'home')

  // 将数据传递给客户端组件
  return <HomeContent pageData={pageData} />
}
