export const i18n = {
  defaultLocale: 'zh-CN',
  locales: ['zh-CN', 'en-US'],
} as const

export type Locale = (typeof i18n)['locales'][number]

export const localeNames: Record<Locale, string> = {
  'zh-CN': '中文',
  'en-US': 'English',
}

export const localeLabels: Record<Locale, string> = {
  'zh-CN': '简体中文',
  'en-US': 'English (US)',
}

export const localeFlags: Record<Locale, string> = {
  'zh-CN': '🇨🇳',
  'en-US': '🇺🇸',
}
