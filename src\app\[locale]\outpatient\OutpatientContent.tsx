'use client'

/**
 * 授权门诊页面内容组件 - 客户端组件
 */
import { motion } from 'motion/react'
import Image from 'next/image'
import { useState, useRef, useEffect } from 'react'
import type { StrapiHero, StrapiBlock } from '@/types/strapi'
import Icon from '@/components/ui/Icon'

// 定义媒体类型
interface Media {
  url: string
  alt?: string
}

// 定义按钮类型
interface Button {
  label: string
  url?: string
}

// 扩展诊所信息类型
interface ClinicBlock extends StrapiBlock {
  __component?: string
  media?: Media
  buttons?: Button[]
  // 诊所特有字段（可选，因为CMS数据可能不包含这些字段）
  name?: string
  address?: string
  phone?: string
  schedule?: string
}

interface OutpatientContentContentProps {
  pageData: {
    title: string
    heroes: StrapiHero[]
    blocks: (StrapiBlock & { __component?: string; media?: Media; buttons?: Button[]; name?: string; address?: string; phone?: string; schedule?: string })[]
  } | null
}

// 动画变体
const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 },
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

// 城市列表（可以从API获取或配置文件中读取）
const cities = ['全部', '杭州']
const citiesEn = ['All', 'Hangzhou']

const BlockRenderer = ({ blocks, locale }: { blocks: ClinicBlock[]; locale: string }) => {
  const [selectedCity, setSelectedCity] = useState(locale === 'zh-CN' ? '全部' : 'All')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // 根据选中的城市过滤诊所
  const filteredBlocks =
    selectedCity === (locale === 'zh-CN' ? '全部' : 'All')
      ? blocks
      : blocks.filter((block) => block.name && block.name.includes(selectedCity))

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="w-full">
      {/* 头部部分 */}
      <motion.div
        className="mb-3 flex  leading-[28px] flex-row items-center justify-between"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* 左侧：总数量 */}
        <div className="flex items-center text-sm font-[300] text-[#444]">
          {locale === 'zh-CN' ? (
            <>
              共<span className="font-normal text-black px-[2px]">{blocks.length}</span>家授权门诊
            </>
          ) : (
            <>
              A total of <span className="font-normal text-black px-1">{blocks.length}</span> authorized
              clinics
            </>
          )}
        </div>

        {/* 右侧：城市下拉列表 */}
        <div className="relative" ref={dropdownRef}>
          {/* 下拉触发按钮 */}
          <div
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="font-sm flex items-center justify-between gap-2 py-2 text-sm text-black cursor-pointer"
          >
            <span>{selectedCity}</span>
            <Icon
              name="xiala2"
              size="lg"
              className={`transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
            />
          </div>

          {/* 下拉选项列表 */}
          {isDropdownOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full right-0 z-50 mt-1 w-full min-w-[120px] rounded-lg border border-gray-200 bg-white py-1 shadow-lg"
            >
              {(locale === 'zh-CN' ? cities : citiesEn).map((city) => (
                <button
                  key={city}
                  onClick={() => {
                    setSelectedCity(city)
                    setIsDropdownOpen(false)
                  }}
                  className={`block w-full px-4 py-2 text-left text-sm transition-colors hover:bg-gray-50 ${
                    selectedCity === city
                      ? 'bg-[#D2A76A]/10 font-medium text-[#D2A76A]'
                      : 'text-gray-700'
                  }`}
                >
                  {city}
                </button>
              ))}
            </motion.div>
          )}
        </div>
      </motion.div>

      {/* 内容部分：诊所卡片列表 */}
      <motion.div
        className="space-y-2"
        variants={staggerContainer}
        initial="initial"
        animate="animate"
      >
        {filteredBlocks.map((block, index) => (
          <motion.div
            key={block.id || index}
            className="bg-white px-6 py-[16px] duration-300 hover:shadow-md"
            variants={fadeInUp}
            whileHover={{ y: -2 }}
            transition={{ duration: 0.2 }}
          >
            {/* 诊所名称*/}
            <div className="mb-5">
              <h3 className="text-[16px] leading-[22px] text-black">{block.name}</h3>
            </div>

            {/* 地址信息 */}
            {block.address && (
              <div className="flex items-start  lg:items-center">
                <Icon name="weizhi" size="sm" className="relative top-[2px] lg:top-[0px]" />
                <p className="ml-3 text-sm leading-[18px] font-[300] text-[#444]">
                  {block.address}
                </p>
              </div>
            )}
            {/* 诊所信息网格 */}
            <div className="lg:mt-[14px] flex lg:flex-row flex-col lg:items-center">
              {/* 电话信息 */}
              {block.phone && (
                <div className="flex w-full lg:w-[50%] items-center my-2 lg:my-0">
                  <Icon name="dianhua" size="sm" />
                  <p className="ml-3 text-sm leading-[18px] font-[300] text-[#444]">
                    {block.phone}
                  </p>
                </div>
              )}

              {/* 营业时间 */}
              {block.schedule && (
                <div className="flex w-full lg:w-[50%] items-center">
                  <Icon name="shijian" size="sm" />
                  <p className="ml-3 text-sm leading-[18px] font-[300] text-[#444]">
                    {block.schedule}
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* 如果没有找到匹配的诊所 */}
      {filteredBlocks.length === 0 && (
        <motion.div
          className="py-12 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
            <svg
              className="h-8 w-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900">{locale === 'zh-CN' ? '暂无相关门诊' : 'No Clinics Found'}</h3>
        </motion.div>
      )}
    </div>
  )
}

// 主要的 OutpatientContentContent 组件
export default function OutpatientContentContent({
  pageData,
  locale,
}: OutpatientContentContentProps & {
  locale: string
}) {
  if (!pageData) {
    return (
      <div className="flex min-h-screen items-center justify-center pt-16 lg:pt-0">
        <p>页面数据加载中...</p>
      </div>
    )
  }
  const { blocks } = pageData
  return (
    <div className="bg-white pt-16 lg:bg-[#000] lg:pt-27">
      {/* Hero Banner */}
      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        {pageData.heroes.map((item: StrapiHero) => (
          <motion.div
            key={item.id}
            className="relative"
            initial={{ scale: 1.05 }}
            animate={{ scale: 1 }}
            transition={{ duration: 2, ease: 'easeOut' }}
          >
            {item?.media?.url && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.2 }}
              >
                <Image
                  key={item.id}
                  src={item.media.url}
                  alt={item.title || ''}
                  width={1200}
                  height={800}
                  sizes="100vw"
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
            <motion.div
              className="absolute top-13 lg:top-10 left-7.5 z-10 w-full text-white md:top-[30%] lg:top-[32%] lg:left-[12%]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <div className="w-full lg:w-[28%]">
                <motion.h1
                  className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                >
                  {item.title}
                </motion.h1>
                <motion.div
                  className="hidden text-sm leading-[28px] font-[300] lg:block lg:text-justify"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.9 }}
                >
                  {item.description}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      {/* 诊所列表 */}
      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <div className="w-full bg-[#FAF6F2] py-10 lg:py-20">
          <div className="mx-auto px-4 lg:w-[76%] lg:px-0">
            <BlockRenderer blocks={blocks} locale={locale} />
          </div>
        </div>
      </motion.div>
    </div>
  )
}
