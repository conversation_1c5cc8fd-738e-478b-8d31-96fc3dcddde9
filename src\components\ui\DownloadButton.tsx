/**
 * 下载按钮组件 - 原子组件
 */

'use client'

import { motion } from 'motion/react'
import { Download, Loader2, CheckCircle } from 'lucide-react'
import { useState } from 'react'
import { useTranslation } from '@/lib/i18n/client'
import type { DownloadButtonProps } from '@/types/image-beautify'

export default function DownloadButton({
  onDownload,
  disabled = false,
  className = ''
}: DownloadButtonProps) {
  const { t } = useTranslation()
  const [isDownloading, setIsDownloading] = useState(false)
  const [downloadSuccess, setDownloadSuccess] = useState(false)

  const handleDownload = async () => {
    if (disabled || isDownloading) return

    try {
      setIsDownloading(true)
      await onDownload()
      
      // 显示成功状态
      setDownloadSuccess(true)
      setTimeout(() => setDownloadSuccess(false), 2000)
    } catch (error) {
      console.error('Download failed:', error)
    } finally {
      setIsDownloading(false)
    }
  }

  const isDisabled = disabled || isDownloading

  return (
    <motion.button
      onClick={handleDownload}
      disabled={isDisabled}
      whileHover={isDisabled ? {} : { scale: 1.02, y: -2 }}
      whileTap={isDisabled ? {} : { scale: 0.98 }}
      className={`
        relative flex items-center justify-center space-x-3 px-8 py-4 rounded-xl
        text-white font-medium text-lg shadow-lg transition-all duration-200
        ${downloadSuccess
          ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 shadow-green-500/25'
          : 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-purple-500/25'
        }
        ${isDisabled 
          ? 'opacity-50 cursor-not-allowed' 
          : 'hover:shadow-xl active:shadow-md'
        }
        ${className}
      `}
    >
      {/* 背景光效 */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-200" />

      {/* 图标 */}
      <div className="relative z-10">
        {isDownloading ? (
          <Loader2 className="w-6 h-6 animate-spin" />
        ) : downloadSuccess ? (
          <CheckCircle className="w-6 h-6" />
        ) : (
          <Download className="w-6 h-6" />
        )}
      </div>

      {/* 文本 */}
      <span className="relative z-10">
        {isDownloading 
          ? t('imageEditor.download.downloading', '下载中...')
          : downloadSuccess
          ? t('imageEditor.download.success', '下载成功')
          : t('imageEditor.download.title', '下载图片')
        }
      </span>

      {/* 下载状态的脉冲效果 */}
      {isDownloading && (
        <motion.div
          className="absolute inset-0 rounded-xl bg-white/20"
          animate={{ opacity: [0.5, 0.8, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        />
      )}

      {/* 成功状态的闪烁效果 */}
      {downloadSuccess && (
        <motion.div
          className="absolute inset-0 rounded-xl bg-white/30"
          initial={{ opacity: 0 }}
          animate={{ opacity: [0, 1, 0] }}
          transition={{ duration: 0.6 }}
        />
      )}
    </motion.button>
  )
}
