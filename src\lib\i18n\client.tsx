'use client'

import React, { createContext, useContext, useMemo } from 'react'
import type { Locale } from './config'
import type { Dictionary } from './types'

interface TranslationContextValue {
  dictionary: Dictionary
  locale: Locale
  t: (key: string, fallback?: string) => string
}

const TranslationContext = createContext<TranslationContextValue | null>(null)

interface TranslationProviderProps {
  children: React.ReactNode
  dictionary: Dictionary
  locale: Locale
}

/**
 * Translation Provider for client components
 * Should be placed at the root of your app
 */
export function TranslationProvider({ children, dictionary, locale }: TranslationProviderProps) {
  const value = useMemo(() => {
    const t = (key: string, fallback?: string) => {
      const keys = key.split('.')
      let value: unknown = dictionary

      for (const k of keys) {
        if (
          value &&
          typeof value === 'object' &&
          k in (value as Record<string, unknown>)
        ) {
          value = (value as Record<string, unknown>)[k]
        } else {
          return fallback || key
        }
      }

      return typeof value === 'string' ? value : fallback || key
    }

    return { dictionary, locale, t }
  }, [dictionary, locale])

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  )
}

/**
 * Hook to access full translation context (dictionary, locale, t function)
 * Usage: const { dictionary, locale, t } = useTranslation()
 */
export function useTranslation(): TranslationContextValue {
  const context = useContext(TranslationContext)

  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider')
  }

  return context
}



/**
 * Lightweight hook to get only the translation function
 * Usage: const t = useT()
 */
export function useT() {
  const context = useContext(TranslationContext)

  if (!context) {
    console.warn('TranslationProvider not found, translation will not work properly')
    return (key: string, fallback?: string) => fallback || key
  }

  return context.t
}
