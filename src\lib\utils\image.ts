/**
 * 图片工具函数
 * 提供与 Strapi 图片处理相关的实用工具
 */

import type { StrapiImage } from '@/types/strapi'

/**
 * 检查 Strapi 图片对象是否有效
 */
export function isValidStrapiImage(image: unknown): image is StrapiImage {
  if (!image || typeof image !== 'object') {
    return false
  }
  
  const img = image as Partial<StrapiImage>
  return !!(
    img.id &&
    img.url &&
    typeof img.url === 'string' &&
    img.width &&
    typeof img.width === 'number' &&
    img.height &&
    typeof img.height === 'number'
  )
}

/**
 * 获取图片的最佳格式 URL
 */
export function getBestImageUrl(
  image: StrapiImage,
  preferredSize: 'thumbnail' | 'small' | 'medium' | 'large' = 'medium'
): string {
  if (!isValidStrapiImage(image)) {
    return ''
  }
  
  // 尝试获取指定尺寸的格式
  const format = image.formats?.[preferredSize]
  if (format?.url) {
    return format.url
  }
  
  // 回退到其他可用格式
  const fallbackOrder: Array<keyof NonNullable<StrapiImage['formats']>> = [
    'large', 'medium', 'small', 'thumbnail'
  ]
  
  for (const size of fallbackOrder) {
    const fallbackFormat = image.formats?.[size]
    if (fallbackFormat?.url) {
      return fallbackFormat.url
    }
  }
  
  // 最后回退到原图
  return image.url
}

/**
 * 获取图片的尺寸信息
 */
export function getImageDimensions(
  image: StrapiImage,
  preferredSize: 'thumbnail' | 'small' | 'medium' | 'large' = 'medium'
): { width: number; height: number } {
  if (!isValidStrapiImage(image)) {
    return { width: 0, height: 0 }
  }
  
  const format = image.formats?.[preferredSize]
  if (format) {
    return {
      width: format.width,
      height: format.height
    }
  }
  
  return {
    width: image.width,
    height: image.height
  }
}

/**
 * 生成图片的 alt 文本
 */
export function generateImageAlt(
  image: StrapiImage,
  fallback: string = '图片'
): string {
  if (!isValidStrapiImage(image)) {
    return fallback
  }
  
  return image.alternativeText || fallback
}

/**
 * 检查图片是否为横向（宽度大于高度）
 */
export function isLandscapeImage(image: StrapiImage): boolean {
  if (!isValidStrapiImage(image)) {
    return false
  }
  
  return image.width > image.height
}

/**
 * 检查图片是否为纵向（高度大于宽度）
 */
export function isPortraitImage(image: StrapiImage): boolean {
  if (!isValidStrapiImage(image)) {
    return false
  }
  
  return image.height > image.width
}

/**
 * 检查图片是否为正方形
 */
export function isSquareImage(image: StrapiImage): boolean {
  if (!isValidStrapiImage(image)) {
    return false
  }
  
  return image.width === image.height
}

/**
 * 计算图片的宽高比
 */
export function getImageAspectRatio(image: StrapiImage): number {
  if (!isValidStrapiImage(image)) {
    return 1
  }
  
  return image.width / image.height
}

/**
 * 根据容器尺寸计算图片的显示尺寸（保持宽高比）
 */
export function calculateImageSize(
  image: StrapiImage,
  containerWidth: number,
  containerHeight: number,
  mode: 'contain' | 'cover' = 'contain'
): { width: number; height: number } {
  if (!isValidStrapiImage(image)) {
    return { width: containerWidth, height: containerHeight }
  }
  
  const imageAspectRatio = getImageAspectRatio(image)
  const containerAspectRatio = containerWidth / containerHeight
  
  if (mode === 'contain') {
    // 图片完全显示在容器内
    if (imageAspectRatio > containerAspectRatio) {
      // 图片更宽，以宽度为准
      return {
        width: containerWidth,
        height: containerWidth / imageAspectRatio
      }
    } else {
      // 图片更高，以高度为准
      return {
        width: containerHeight * imageAspectRatio,
        height: containerHeight
      }
    }
  } else {
    // 图片覆盖整个容器
    if (imageAspectRatio > containerAspectRatio) {
      // 图片更宽，以高度为准
      return {
        width: containerHeight * imageAspectRatio,
        height: containerHeight
      }
    } else {
      // 图片更高，以宽度为准
      return {
        width: containerWidth,
        height: containerWidth / imageAspectRatio
      }
    }
  }
}

/**
 * 生成响应式图片的 sizes 属性
 */
export function generateResponsiveSizes(config: {
  mobile?: string
  tablet?: string
  desktop?: string
  fallback?: string
}): string {
  const {
    mobile = '100vw',
    tablet = '50vw',
    desktop = '33vw',
    fallback: defaultSize = '25vw'
  } = config

  return [
    `(max-width: 640px) ${mobile}`,
    `(max-width: 1024px) ${tablet}`,
    `(max-width: 1280px) ${desktop}`,
    defaultSize
  ].join(', ')
}

/**
 * 预设的响应式尺寸配置
 */
export const RESPONSIVE_SIZES_PRESETS = {
  hero: '100vw',
  fullWidth: '100vw',
  card: '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw',
  thumbnail: '(max-width: 640px) 25vw, (max-width: 1024px) 20vw, 15vw',
  gallery: '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw',
  sidebar: '(max-width: 1024px) 100vw, 25vw',
  avatar: '(max-width: 640px) 15vw, 10vw'
} as const

/**
 * 获取预设的响应式尺寸
 */
export function getPresetSizes(preset: keyof typeof RESPONSIVE_SIZES_PRESETS): string {
  return RESPONSIVE_SIZES_PRESETS[preset]
}
