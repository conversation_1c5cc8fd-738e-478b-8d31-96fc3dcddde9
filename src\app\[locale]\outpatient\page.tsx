/**
 * 授权门诊页面 - 服务端组件（仅负责数据获取）
 */
import { getPage } from '@/lib/cms/page'
import type { Metadata } from 'next'
import OutpatientContent from './OutpatientContent'

interface OutpatientPageProps {
  params: Promise<{ locale: string }>
}

// 生成页面元数据
export async function generateMetadata({ params }: OutpatientPageProps): Promise<Metadata> {
  const { locale } = await params
  const pageData = await getPage(locale, 'outpatient')
  if (pageData?.title) {
    return {
      title: pageData.title,
      description: pageData.heroes[0]?.description || pageData.title,
    }
  }

  // 回退到默认标题
  return {
    title: '授权门诊',
    description: '探索 Lumii 的授权门诊',
  }
}

// 服务端组件 - 只负责数据获取
export default async function Outpatient({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const pageData = await getPage(locale, 'outpatient')

  // 将数据传递给客户端组件
  return <OutpatientContent pageData={pageData} locale={locale} />
}
