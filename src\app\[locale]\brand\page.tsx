/**
 * 品牌页面 - 服务端组件（仅负责数据获取）
 */
import { getPage } from '@/lib/cms/page'
import type { Metadata } from 'next'
import BrandContent from './BrandContent'

interface BrandPageProps {
  params: Promise<{ locale: string }>
}

// 生成页面元数据
export async function generateMetadata({ params }: BrandPageProps): Promise<Metadata> {
  const { locale } = await params
  const pageData = await getPage(locale, 'brand')

  if (pageData?.title) {
    return {
      title: pageData.title,
      description: pageData.heroes[0]?.description || pageData.title,
    }
  }

  // 回退到默认标题
  return {
    title: '品牌故事',
    description: '探索 Lumii 的品牌故事',
  }
}

// 服务端组件 - 只负责数据获取
export default async function Brand({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const pageData = await getPage(locale, 'brand')

  // 将数据传递给客户端组件
  return <BrandContent pageData={pageData} />
}
