import { type Locale } from './config'

/**
 * Generate alternate language links for SEO
 */
export function generateAlternateLinks(locale: Locale, pathname: string = '') {
  const alternates: Record<string, string> = {}
  
  // Remove locale from pathname if it exists
  const cleanPathname = pathname.replace(/^\/[a-z]{2}-[A-Z]{2}/, '') || '/'
  
  alternates['zh-CN'] = `/zh-CN${cleanPathname === '/' ? '' : cleanPathname}`
  alternates['en-US'] = `/en-US${cleanPathname === '/' ? '' : cleanPathname}`
  
  return alternates
}

/**
 * Get the opposite locale for language switching
 */
export function getAlternateLocale(currentLocale: Locale): Locale {
  return currentLocale === 'zh-CN' ? 'en-US' : 'zh-CN'
}

/**
 * Check if a locale is valid
 */
export function isValidLocale(locale: string): locale is Locale {
  return ['zh-CN', 'en-US'].includes(locale)
}

/**
 * Extract locale from pathname
 */
export function extractLocaleFromPathname(pathname: string): Locale | null {
  const segments = pathname.split('/')
  const potentialLocale = segments[1]
  
  if (isValidLocale(potentialLocale)) {
    return potentialLocale
  }
  
  return null
}

/**
 * Remove locale from pathname
 */
export function removeLocaleFromPathname(pathname: string): string {
  const locale = extractLocaleFromPathname(pathname)
  if (locale) {
    return pathname.replace(`/${locale}`, '') || '/'
  }
  return pathname
}
