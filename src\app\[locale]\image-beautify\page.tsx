/**
 * 图片美化页面
 */

import type { Metadata } from 'next'
import { getDictionary } from '@/lib/i18n/dictionaries'
import { i18n, type Locale } from '@/lib/i18n/config'
import { notFound } from 'next/navigation'
import ImageBeautifySection from './components/ImageBeautifySection'

interface ImageBeautifyPageProps {
  params: Promise<{ locale: Locale }>
}

// 生成静态参数
export async function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }))
}

// 生成页面元数据
export async function generateMetadata({ params }: ImageBeautifyPageProps): Promise<Metadata> {
  const { locale } = await params
  
  // 验证语言参数
  if (!i18n.locales.includes(locale)) {
    notFound()
  }

  const dictionary = await getDictionary(locale)

  return {
    title: `${dictionary.imageEditor.title} - ${dictionary.metadata.title}`,
    description: dictionary.imageEditor.subtitle,
    keywords: `${dictionary.metadata.keywords}, 图片美化, 牙齿美白, 美颜, AI处理`,
    openGraph: {
      title: `${dictionary.imageEditor.title} - ${dictionary.metadata.title}`,
      description: dictionary.imageEditor.subtitle,
      type: 'website',
      locale: locale,
    },
    twitter: {
      card: 'summary_large_image',
      title: `${dictionary.imageEditor.title} - ${dictionary.metadata.title}`,
      description: dictionary.imageEditor.subtitle,
    },
    robots: {
      index: true,
      follow: true,
    },
    alternates: {
      canonical: `/${locale}/image-beautify`,
      languages: {
        'zh-CN': '/zh-CN/image-beautify',
        'en-US': '/en-US/image-beautify',
      },
    },
  }
}

export default async function ImageBeautifyPage({ params }: ImageBeautifyPageProps) {
  const { locale } = await params

  // 验证语言参数
  if (!i18n.locales.includes(locale)) {
    notFound()
  }

  return (
    <>
      {/* 使用绝对定位覆盖整个视口，隐藏 Footer */}
      <div className="fixed inset-0 bg-black z-40">
        <ImageBeautifySection />
      </div>
    </>
  )
}
