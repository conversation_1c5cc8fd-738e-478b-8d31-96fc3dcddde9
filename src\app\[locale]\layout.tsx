import type { <PERSON><PERSON><PERSON>, View<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import localFont from "next/font/local";
import "../globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { Toaster } from "@/components/ui/sonner";
import { getDictionary } from "@/lib/i18n/dictionaries";
import { i18n, type Locale } from "@/lib/i18n/config";
import { TranslationProvider } from "@/lib/i18n/client";
import { HeaderHeightProvider } from "@/lib/contexts/HeaderHeightContext";
import { notFound } from "next/navigation";
import { getHeader, getFooter, getSite } from "@/lib/cms/layout";
import MobileTouchOptimizer from "@/components/utils/MobileTouchOptimizer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// 配置 iconfont
const iconfont = localFont({
  src: [
    {
      path: "../../../public/font/iconfont.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../../public/font/iconfont.woff",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../../public/font/iconfont.ttf",
      weight: "400",
      style: "normal",
    },
  ],
  variable: "--font-iconfont",
  display: "swap",
});

export async function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);
  
  return {
    title: dictionary.metadata.title,
    description: dictionary.metadata.description,
    keywords: dictionary.metadata.keywords,
    authors: [{ name: "Lumii Team" }],
    icons: {
      icon: '/favicon.ico',
      shortcut: '/favicon.ico',
      apple: '/favicon.ico',
    },
    alternates: {
      languages: {
        'zh-CN': '/zh-CN',
        'en-US': '/en-US',
      },
    },
  };
}

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: Locale }>;
}>) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!i18n.locales.includes(locale)) {
    notFound();
  }

  const dictionary = await getDictionary(locale);

  // 获取Header、Footer、Site数据
  const headerData = await getHeader(locale);
  const footerData = await getFooter(locale);
  const siteData = await getSite(locale);
  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${iconfont.variable} antialiased`}
      >
        <TranslationProvider dictionary={dictionary} locale={locale}>
          <HeaderHeightProvider>
            <MobileTouchOptimizer />
            <Header data={headerData} siteData={siteData} />
            <main>
              {children}
            </main>
            <Footer data={footerData} siteData={siteData} />
            <Toaster />
          </HeaderHeightProvider>
        </TranslationProvider>
      </body>
    </html>
  );
}
