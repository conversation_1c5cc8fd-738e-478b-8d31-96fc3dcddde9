/**
 * 通用响应式图片组件
 * 作为 Next.js Image 组件的完整封装器，专门优化 Strapi CMS 图片数据
 * 提供响应式图片支持、性能优化和错误处理
 */

'use client'

import Image, { type ImageProps } from 'next/image'
import { useState } from 'react'
import type { StrapiImage } from '@/types/strapi'





interface ResponsiveImageProps extends Omit<ImageProps, 'src' | 'alt'> {
  /** Strapi 图片对象 */
  image: StrapiImage
  /** 图片替代文本 */
  alt: string
}







/**
 * 获取图片源 - 直接使用原图，让 Next.js 自动优化
 */
function getBestImageSrc(image: StrapiImage): string {
  // 直接使用原图，Next.js 会根据需要自动优化和调整尺寸
  return image.url
}

/**
 * 通用响应式图片组件
 * 作为 Next.js Image 组件的完整封装器，专门优化 Strapi CMS 图片数据
 */
export default function ResponsiveImage({
  image,
  alt,
  fill = true, // 默认使用 fill 模式
  style = { objectFit: 'cover' }, // 默认样式
  className = '',
  ...nextImageProps // 透传所有其他 Next.js Image 属性
}: ResponsiveImageProps) {
  const [imageError, setImageError] = useState(false)

  // 错误处理
  if (!image || imageError) {
    return (
      <div
        className={`bg-gray-200 dark:bg-gray-800 rounded-lg flex items-center justify-center ${className}`}
        style={style}
      >
        <div className="text-gray-500 dark:text-gray-400 text-center p-4">
          <svg className="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-sm">图片加载失败</p>
        </div>
      </div>
    )
  }

  // 获取图片源 - 使用原图让 Next.js 优化
  const src = getBestImageSrc(image)

  // 图片替代文本
  const altText = alt || image.alternativeText || '图片'

  // 处理加载错误
  const handleError = (error: React.SyntheticEvent<HTMLImageElement, Event>) => {
    setImageError(true)
    if (nextImageProps.onError) {
      nextImageProps.onError(error)
    }
  }

  return (
    <Image
      {...nextImageProps} // 透传所有 Next.js Image 属性
      src={src}
      alt={altText}
      fill={fill}
      style={style}
      className={className}
      onError={handleError}
    />
  )
}


