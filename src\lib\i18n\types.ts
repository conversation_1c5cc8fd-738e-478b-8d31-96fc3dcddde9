import type zhC<PERSON> from './dictionaries/zh-CN.json'

export type Dictionary = typeof zhCN

export type DictionaryKey = keyof Dictionary

export type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`
}[keyof ObjectType & (string | number)]

export type TranslationKey = NestedKeyOf<Dictionary>

// Helper type for getting nested values
export type GetNestedValue<T, K extends string> = K extends keyof T
  ? T[K]
  : K extends `${infer P}.${infer S}`
  ? P extends keyof T
    ? GetNestedValue<T[P], S>
    : never
  : never
