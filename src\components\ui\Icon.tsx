import React from 'react';
import { cn } from '@/lib/utils';

// 定义所有可用的图标名称
export type IconName = 
  | 'yijianmeiyan1'
  | 'douyin'
  | 'xiala2'
  | 'xiaochengxu'
  | 'wancheng'
  | 'xiala'
  | 'zanting'
  | 'dianhua'
  | 'yachimeibai'
  | 'weixin'
  | 'weizhi'
  | 'xiaohongshu'
  | 'shijian'
  | 'shangchuanzhaopian'
  | 'bofang'
  | 'shouqi'
  | 'guanbi'
  | 'caidan'
  | 'duibi'
  | 'weibo'
  | 'qiehuan';

// 定义图标尺寸
export type IconSize = 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';

export interface IconProps {
  /** 图标名称 */
  name: IconName;
  /** 图标尺寸 */
  size?: IconSize;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 点击事件 */
  onClick?: () => void;
}

/**
 * Iconfont 图标组件
 * 
 * @example
 * ```tsx
 * <Icon name="weixin" size="lg" className="text-green-500" />
 * <Icon name="bofang" size="2xl" onClick={() => console.log('播放')} />
 * ```
 */
export const Icon: React.FC<IconProps> = ({ 
  name, 
  size = 'base', 
  className = '', 
  style,
  onClick 
}) => {
  const sizeClass = size ? `iconfont-${size}` : '';
  
  return (
    <i 
      className={cn(
        'iconfont',
        `icon-${name}`,
        sizeClass,
        onClick && 'cursor-pointer',
        className
      )}
      style={style}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      } : undefined}
    />
  );
};

export default Icon;
