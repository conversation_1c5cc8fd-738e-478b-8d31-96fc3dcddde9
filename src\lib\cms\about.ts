/**
 * About Us页面的CMS API函数
 */

import type { AboutUsPage } from '@/types/strapi'
import { fetchStrapiCollection } from './client'
import { API_ENDPOINTS, buildSiteFilteredParams } from './config'

/**
 * 获取About Us页面数据
 */
export const getAboutUsPage = async (locale: string): Promise<AboutUsPage | null> => {
  try {
    const response = await fetchStrapiCollection<AboutUsPage>(
      API_ENDPOINTS.ABOUT_US,
      locale,
      buildSiteFilteredParams()
    )

    // 返回第一个匹配的页面数据
    return response.data[0] || null
  } catch (error) {
    console.error('Failed to fetch About Us page:', error)
    return null
  }
}

/**
 * 获取About Us页面的所有可用语言版本
 */
export const getAboutUsPageLocales = async (): Promise<string[]> => {
  try {
    // 获取所有语言版本
    const [enResponse, zhResponse] = await Promise.allSettled([
      fetchStrapiCollection<AboutUsPage>(API_ENDPOINTS.ABOUT_US, 'en-US', buildSiteFilteredParams()),
      fetchStrapiCollection<AboutUsPage>(API_ENDPOINTS.ABOUT_US, 'zh-CN', buildSiteFilteredParams())
    ])

    const availableLocales: string[] = []

    if (enResponse.status === 'fulfilled' && enResponse.value.data.length > 0) {
      availableLocales.push('en-US')
    }

    if (zhResponse.status === 'fulfilled' && zhResponse.value.data.length > 0) {
      availableLocales.push('zh-CN')
    }

    return availableLocales
  } catch (error) {
    console.error('Failed to fetch About Us page locales:', error)
    return []
  }
}

/**
 * 获取About Us页面的Heroes数据
 */
export const getAboutUsHeroes = async (locale: string) => {
  try {
    const page = await getAboutUsPage(locale)
    return page?.heroes || []
  } catch (error) {
    console.error('Failed to fetch About Us heroes:', error)
    return []
  }
}


