/**
 * 顶部操作栏组件
 */

'use client'

import { X } from 'lucide-react'
import Button from '@/components/ui/Button'
import { useTranslation } from '@/lib/i18n/client'
import { useHasProcessedImage } from '@/lib/stores/image-beautify'

interface TopActionBarProps {
  onReset: () => void
  onSave: () => void
  hasProcessedImage: boolean
  top?: number
  className?: string
}

export default function TopActionBar({
  onReset,
  onSave,
  hasProcessedImage,
  top = 0,
  className = ''
}: TopActionBarProps) {
  const { t } = useTranslation()
  const storeHasProcessedImage = useHasProcessedImage()

  // 优先使用 store 的状态，如果没有则使用 props
  const currentHasProcessedImage = hasProcessedImage ?? storeHasProcessedImage

  return (
    <div
      className={`
        fixed left-0 right-0 z-40
        bg-black/0 px-4 py-3
        flex items-center justify-between
        ${className}
      `}
      style={{ top: `${top}px` }}
    >
      {/* 左侧：重新上传按钮 */}
      <button
        onClick={onReset}
        className="flex items-center justify-center w-8 h-8 text-white hover:bg-gray-800 rounded transition-colors cursor-pointer"
        aria-label={t('imageEditor.actions.reupload', '重新上传')}
      >
        <X className="w-5 h-5" />
      </button>

      {/* 右侧：保存按钮 */}
      {currentHasProcessedImage && (
        <Button
          onClick={onSave}
          variant="secondary"
          size="sm"
          className="px-6"
        >
          {t('imageEditor.actions.save', '保存')}
        </Button>
      )}
    </div>
  )
}
