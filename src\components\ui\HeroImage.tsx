/**
 * Hero 图片组件
 * 用于显示 About Us 页面的 Heroes 图片
 * 基于通用 ResponsiveImage 组件
 */

'use client'

import ResponsiveImage from './ResponsiveImage'
import type { StrapiHero } from '@/types/strapi'

interface HeroImageProps {
  heroes: StrapiHero[]
  className?: string
  priority?: boolean
}

export default function HeroImage({ heroes, className = '', priority = false }: HeroImageProps) {
  // 获取第一个有图片的 hero
  const heroWithImage = heroes.find(hero => hero.media)

  if (!heroWithImage?.media) {
    return (
      <div className={`bg-gray-200 dark:bg-gray-800 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-gray-500 dark:text-gray-400 text-center p-8">
          <svg className="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-sm">暂无图片</p>
        </div>
      </div>
    )
  }

  const image = heroWithImage.media
  const altText = image.alternativeText || heroWithImage.title || '英雄图片'

  return (
    <div className={`relative w-full overflow-hidden ${className}`}>
      <ResponsiveImage
        image={image}
        alt={altText}
        priority={priority}
        fill
        style={{ objectFit: 'cover' }}
        sizes="100vw"
      />
    </div>
  )
}
