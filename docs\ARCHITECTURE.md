# 技术架构文档

## 🎯 架构概述

Lumii Official Website 采用现代化的前端架构设计，基于 Next.js 15 App Router 构建，遵循组件化、模块化和可扩展的设计原则。本文档详细说明了技术选型、架构设计和实现策略。

## 🏗️ 系统架构

### 架构层次
```
┌─────────────────────────────────────┐
│           用户界面层 (UI Layer)        │
├─────────────────────────────────────┤
│         组件层 (Component Layer)      │
├─────────────────────────────────────┤
│         业务逻辑层 (Logic Layer)       │
├─────────────────────────────────────┤
│         数据层 (Data Layer)           │
├─────────────────────────────────────┤
│         基础设施层 (Infrastructure)    │
└─────────────────────────────────────┘
```

### 技术选型决策

#### 1. Next.js 15.4.3 - 核心框架
**选择原因:**
- **App Router**: 新一代路由系统，支持嵌套布局和流式渲染
- **Server Components**: 减少客户端 JavaScript 包大小
- **内置优化**: 图片优化、字体优化、代码分割
- **全栈能力**: 支持 API 路由和服务端逻辑
- **生态成熟**: 丰富的插件和社区支持

#### 2. React 19.1.0 - UI 库
**选择原因:**
- **最新特性**: 支持 Concurrent Features 和 Suspense
- **性能优化**: 自动批处理和优化的渲染机制
- **开发体验**: 更好的错误边界和调试工具
- **生态系统**: 庞大的组件库和工具生态

#### 3. TypeScript 5.x - 类型系统
**选择原因:**
- **类型安全**: 编译时错误检查，减少运行时错误
- **开发效率**: 智能提示和重构支持
- **代码质量**: 强制类型约束，提高代码可维护性
- **团队协作**: 统一的接口定义和文档

#### 4. Tailwind CSS 4.x - 样式框架
**选择原因:**
- **原子化设计**: 高度可复用的样式类
- **性能优化**: 按需生成 CSS，减少包大小
- **设计系统**: 内置的设计令牌和约束
- **开发效率**: 快速原型和一致的样式

#### 5. Motion (Framer Motion) - 动画库
**选择原因:**
- **声明式 API**: 简洁的动画定义方式
- **性能优化**: 基于 Web Animations API
- **手势支持**: 丰富的交互动画
- **React 集成**: 与 React 生命周期完美结合

## 📁 目录架构设计

### 分层目录结构
```
src/
├── app/                    # Next.js App Router (路由层)
│   ├── (routes)/          # 路由组织
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 页面组件
├── components/            # 组件层
│   ├── ui/               # 原子组件 (Atoms)
│   ├── layout/           # 布局组件 (Templates)
│   ├── sections/         # 区块组件 (Organisms)
│   └── forms/            # 表单组件
├── lib/                  # 工具层
│   ├── utils.ts          # 通用工具
│   ├── constants.ts      # 常量定义
│   └── validations.ts    # 验证逻辑
├── types/                # 类型定义层
│   ├── index.ts          # 全局类型
│   ├── api.ts            # API 类型
│   └── components.ts     # 组件类型
├── hooks/                # 自定义 Hooks
├── stores/               # 状态管理
└── styles/               # 样式资源
```

### 设计原则
1. **单一职责**: 每个目录和文件都有明确的职责
2. **分层隔离**: 不同层次之间通过接口交互
3. **依赖倒置**: 高层模块不依赖低层模块
4. **开闭原则**: 对扩展开放，对修改封闭

## 🧩 组件架构设计

### 组件分层模型 (Atomic Design)

#### 1. 原子组件 (Atoms) - `components/ui/`
```typescript
// 示例：Button 组件
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline'
  size: 'sm' | 'md' | 'lg'
  children: ReactNode
  onClick?: () => void
}
```
- **职责**: 最小的可复用 UI 单元
- **特点**: 无业务逻辑，高度可复用
- **示例**: Button, Input, Icon, Badge

#### 2. 分子组件 (Molecules) - `components/forms/`
```typescript
// 示例：SearchBox 组件
interface SearchBoxProps {
  placeholder: string
  onSearch: (query: string) => void
}
```
- **职责**: 由原子组件组合而成的功能单元
- **特点**: 包含简单的交互逻辑
- **示例**: SearchBox, FormField, Card

#### 3. 有机体组件 (Organisms) - `components/sections/`
```typescript
// 示例：Hero 组件
interface HeroProps {
  title: string
  subtitle: string
  ctaButton: ButtonProps
}
```
- **职责**: 复杂的 UI 区块，包含业务逻辑
- **特点**: 可独立使用的页面区块
- **示例**: Header, Hero, ProductGrid

#### 4. 模板组件 (Templates) - `components/layout/`
```typescript
// 示例：PageLayout 组件
interface PageLayoutProps {
  children: ReactNode
  sidebar?: ReactNode
}
```
- **职责**: 页面结构和布局定义
- **特点**: 定义页面骨架，不包含具体内容
- **示例**: PageLayout, AuthLayout

### 组件设计原则

#### 1. 单一职责原则 (SRP)
每个组件只负责一个明确的功能，避免功能耦合。

#### 2. 开闭原则 (OCP)
组件对扩展开放，对修改封闭，通过 props 和插槽实现扩展。

#### 3. 依赖倒置原则 (DIP)
高层组件不依赖低层组件的具体实现，通过接口进行交互。

#### 4. 组合优于继承
使用组合模式构建复杂组件，而不是继承。

## 🎨 样式架构设计

### CSS 架构策略

#### 1. Tailwind CSS 4.x 核心架构
```css
/* globals.css - 样式入口 */
@import "tailwindcss";

/* CSS 变量系统 */
:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #3b82f6;
  --secondary: #6b7280;
}

/* 主题配置 */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}
```

#### 2. 设计令牌系统
```typescript
// 设计系统配置
export const designTokens = {
  colors: {
    primary: {
      50: '#eff6ff',
      500: '#3b82f6',
      900: '#1e3a8a'
    }
  },
  spacing: {
    xs: '0.5rem',
    sm: '1rem',
    md: '1.5rem',
    lg: '2rem',
    xl: '3rem'
  },
  typography: {
    fontSizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem'
    }
  }
}
```

#### 3. 响应式设计策略
- **移动优先**: 默认样式针对移动设备
- **断点系统**: sm(640px), md(768px), lg(1024px), xl(1280px)
- **容器查询**: 支持组件级响应式设计
- **流体排版**: 使用 clamp() 实现自适应字体大小

#### 4. 暗色模式架构
```css
/* 自动暗色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* 手动切换支持 */
[data-theme="dark"] {
  --background: #0a0a0a;
  --foreground: #ededed;
}
```

## 🎭 动画架构设计

### Motion 动画系统

#### 1. 动画分层架构
```typescript
// 动画配置层次
export const animations = {
  // 基础动画
  transitions: {
    default: { duration: 0.3, ease: "easeInOut" },
    slow: { duration: 0.6, ease: "easeInOut" },
    spring: { type: "spring", stiffness: 300, damping: 30 }
  },

  // 预设动画
  presets: {
    fadeIn: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 }
    },
    slideUp: {
      initial: { y: 20, opacity: 0 },
      animate: { y: 0, opacity: 1 },
      exit: { y: -20, opacity: 0 }
    }
  }
}
```

#### 2. 性能优化策略
- **GPU 加速**: 优先使用 transform 和 opacity
- **will-change**: 合理使用 CSS will-change 属性
- **动画分组**: 批量处理相关动画
- **懒加载**: 视口内动画按需触发

#### 3. 用户体验考虑
```typescript
// 尊重用户偏好
const prefersReducedMotion = useReducedMotion()

const animationProps = prefersReducedMotion
  ? { initial: false, animate: false }
  : { initial: "hidden", animate: "visible" }
```

#### 4. 动画设计原则
- **有意义**: 动画应该有明确的目的和意义
- **一致性**: 保持动画风格和时长的一致性
- **可访问性**: 支持减少动画偏好设置
- **性能**: 避免影响页面性能的动画

## 🔧 工具链架构

### 开发工具链设计

#### 1. 类型安全工具链
```json
// tsconfig.json - 严格类型配置
{
  "compilerOptions": {
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

#### 2. 代码质量工具
```javascript
// eslint.config.mjs - 代码规范
export default [
  {
    rules: {
      "@typescript-eslint/no-unused-vars": "error",
      "react-hooks/exhaustive-deps": "warn",
      "prefer-const": "error"
    }
  }
]
```

#### 3. 构建优化工具
- **SWC**: 替代 Babel 的高性能编译器
- **Turbopack**: 下一代构建工具（可选）
- **Bundle Analyzer**: 包大小分析工具

## 📊 性能架构策略

### 1. 渲染性能优化

#### Server Components 策略
```typescript
// 服务端组件 - 减少客户端 JavaScript
export default async function ProductList() {
  const products = await fetchProducts() // 服务端数据获取
  return (
    <div>
      {products.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
```

#### 客户端组件优化
```typescript
'use client'
// 仅在需要交互时使用客户端组件
export default function InteractiveButton() {
  const [count, setCount] = useState(0)
  return <button onClick={() => setCount(c => c + 1)}>{count}</button>
}
```

### 2. 加载性能优化

#### 代码分割策略
```typescript
// 路由级分割
const LazyComponent = lazy(() => import('./HeavyComponent'))

// 组件级分割
const Modal = dynamic(() => import('./Modal'), {
  loading: () => <Skeleton />,
  ssr: false
})
```

#### 资源优化
- **图片优化**: Next.js Image 组件自动优化
- **字体优化**: 本地字体文件，减少网络请求
- **CSS 优化**: Tailwind CSS 按需生成

### 3. 缓存策略
```typescript
// 数据缓存
export const revalidate = 3600 // 1小时重新验证

// 静态生成
export async function generateStaticParams() {
  return [{ slug: 'about' }, { slug: 'contact' }]
}
```

## 🔒 类型安全架构

### 类型定义策略

#### 1. 分层类型定义
```typescript
// types/index.ts - 全局类型
export interface User {
  id: string
  name: string
  email: string
}

// types/api.ts - API 类型
export interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
}

// types/components.ts - 组件类型
export interface ButtonProps {
  variant: 'primary' | 'secondary'
  size: 'sm' | 'md' | 'lg'
  children: ReactNode
}
```

#### 2. 类型安全实践
- **严格模式**: 启用所有 TypeScript 严格检查
- **类型守卫**: 运行时类型检查
- **泛型约束**: 提供更好的类型推断
- **品牌类型**: 防止类型混淆

## 🚀 扩展性架构设计

### 1. 功能扩展策略

#### 状态管理扩展
```typescript
// 使用 Zustand 进行状态管理
interface AppState {
  user: User | null
  theme: 'light' | 'dark'
  setUser: (user: User) => void
  setTheme: (theme: 'light' | 'dark') => void
}

export const useAppStore = create<AppState>((set) => ({
  user: null,
  theme: 'light',
  setUser: (user) => set({ user }),
  setTheme: (theme) => set({ theme })
}))
```

#### 国际化架构
```typescript
// 国际化配置 - lib/i18n/config.ts
export const i18n = {
  defaultLocale: 'zh-CN',
  locales: ['zh-CN', 'en-US'],
} as const

export type Locale = (typeof i18n)['locales'][number]

// 翻译字典类型 - lib/i18n/types.ts
export type Dictionary = typeof import('./dictionaries/zh-CN.json')

// 服务端翻译 - lib/i18n/server.ts
export const getServerTranslation = cache(async () => {
  const dictionary = await getServerDictionary()
  const locale = await getCurrentLocale()
  return { dictionary, locale, t: (key: string) => getNestedValue(dictionary, key) }
})

// 客户端翻译 - lib/i18n/client.tsx
export function TranslationProvider({ children, dictionary, locale }) {
  const value = useMemo(() => ({ dictionary, locale, t }), [dictionary, locale])
  return <TranslationContext.Provider value={value}>{children}</TranslationContext.Provider>
}

export function useTranslation(): TranslationContextValue {
  const context = useContext(TranslationContext)
  if (!context) throw new Error('useTranslation must be used within a TranslationProvider')
  return context
}
```

**架构特点:**
- **混合架构**: 服务端使用 React cache，客户端使用 Context
- **零 Prop Drilling**: 组件直接访问翻译数据
- **类型安全**: 完整的 TypeScript 类型推断
- **性能优化**: 请求级缓存和 useMemo 优化
- **URL 路由**: 支持 `/zh-CN/` 和 `/en-US/` 路径结构

### 2. 技术栈扩展

#### 数据获取层
```typescript
// SWR 配置
export const swrConfig = {
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  refreshInterval: 0,
  errorRetryCount: 3
}

// React Query 配置
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
    }
  }
})
```

#### 测试架构
```typescript
// 测试工具配置
export const testConfig = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
}
```

### 3. 监控和分析

#### 性能监控
```typescript
// Web Vitals 监控
export function reportWebVitals(metric: Metric) {
  switch (metric.name) {
    case 'CLS':
    case 'FID':
    case 'FCP':
    case 'LCP':
    case 'TTFB':
      // 发送到分析服务
      analytics.track(metric.name, metric.value)
      break
  }
}
```

#### 错误监控
```typescript
// Sentry 配置
Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1
})
```

## 🔄 架构演进策略

### 1. 微前端架构准备
- 模块联邦支持
- 独立部署能力
- 共享组件库

### 2. 服务端架构扩展
- API 路由优化
- 数据库集成
- 缓存策略

### 3. 移动端扩展
- PWA 支持
- 响应式优化
- 触摸交互

## 📋 架构决策记录 (ADR)

### ADR-001: 选择 Next.js App Router
**状态**: 已接受
**决策**: 使用 Next.js 15 App Router 替代 Pages Router
**原因**: 更好的性能、嵌套布局、流式渲染支持

### ADR-002: 选择 Tailwind CSS 4.x
**状态**: 已接受
**决策**: 使用 Tailwind CSS 作为主要样式解决方案
**原因**: 原子化设计、性能优化、设计系统一致性

### ADR-003: 选择 Motion 动画库
**状态**: 已接受
**决策**: 使用 Motion (Framer Motion) 处理动画
**原因**: 声明式 API、性能优化、React 集成度高

### ADR-004: 国际化架构设计
**状态**: 已接受
**决策**: 采用混合国际化架构，服务端使用 React cache，客户端使用 Context
**原因**:
- 消除 prop drilling 问题
- 支持服务端和客户端组件
- 类型安全的翻译系统
- 性能优化的缓存机制

---

## 📚 相关文档

- [开发指南](./DEVELOPMENT.md) - 详细的开发环境配置和工作流程
- [项目概览](../README.md) - 项目基本信息和快速开始
- [API 文档](#) - 接口文档（待完善）
- [部署指南](#) - 生产环境部署说明（待完善）

---

*本架构文档会随着项目发展持续更新，记录重要的技术决策和架构演进。*
