/**
 * 统一的 API 客户端
 */

import type { ApiResponse, RequestConfig } from './types'

// API 配置
const DEFAULT_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
}

// 自定义错误类
export class ApiClientError extends Error {
  constructor(
    message: string,
    public code: string,
    public status?: number,
    public details?: unknown
  ) {
    super(message)
    this.name = 'ApiClientError'
  }
}

// 延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 创建 AbortController 支持超时
const createTimeoutController = (timeout: number) => {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)
  return { controller, timeoutId }
}

/**
 * 统一的请求方法
 */
async function request<T = unknown>(
  endpoint: string,
  config: RequestConfig = {}
): Promise<ApiResponse<T>> {
  const {
    timeout = DEFAULT_CONFIG.timeout,
    retries = DEFAULT_CONFIG.retries,
    retryDelay = DEFAULT_CONFIG.retryDelay,
    baseURL = DEFAULT_CONFIG.baseURL,
    ...fetchConfig
  } = config

  const url = endpoint.startsWith('http') ? endpoint : `${baseURL}${endpoint}`
  
  let lastError: Error | null = null

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const { controller, timeoutId } = createTimeoutController(timeout)
      
      const response = await fetch(url, {
        ...fetchConfig,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...fetchConfig.headers,
        },
      })

      clearTimeout(timeoutId)

      // 处理 HTTP 错误状态
      if (!response.ok) {
        let errorData: { message?: string; code?: string } = {}
        try {
          errorData = await response.json()
        } catch {
          // 如果响应不是 JSON，使用状态文本
          errorData = { message: response.statusText }
        }

        throw new ApiClientError(
          errorData.message || `HTTP ${response.status}`,
          errorData.code || 'HTTP_ERROR',
          response.status,
          errorData
        )
      }

      // 解析响应
      const data = await response.json()
      return data as ApiResponse<T>

    } catch (error) {
      lastError = error as Error
      
      // 如果是最后一次尝试，抛出错误
      if (attempt === retries) {
        break
      }

      // 如果是 AbortError（超时），不重试
      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiClientError(
          'Request timeout',
          'TIMEOUT',
          408,
          { timeout }
        )
      }

      // 如果是客户端错误（4xx），不重试
      if (error instanceof ApiClientError && error.status && error.status < 500) {
        throw error
      }

      // 等待后重试
      await delay(retryDelay * Math.pow(2, attempt)) // 指数退避
    }
  }

  // 如果所有重试都失败了
  if (lastError instanceof ApiClientError) {
    throw lastError
  }

  throw new ApiClientError(
    lastError?.message || 'Request failed',
    'REQUEST_FAILED',
    undefined,
    lastError
  )
}

/**
 * GET 请求
 */
export const get = <T = unknown>(endpoint: string, config?: RequestConfig) =>
  request<T>(endpoint, { ...config, method: 'GET' })

/**
 * POST 请求
 */
export const post = <T = unknown>(endpoint: string, data?: unknown, config?: RequestConfig) =>
  request<T>(endpoint, {
    ...config,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  })

/**
 * PUT 请求
 */
export const put = <T = unknown>(endpoint: string, data?: unknown, config?: RequestConfig) =>
  request<T>(endpoint, {
    ...config,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  })

/**
 * DELETE 请求
 */
export const del = <T = unknown>(endpoint: string, config?: RequestConfig) =>
  request<T>(endpoint, { ...config, method: 'DELETE' })

/**
 * 上传文件的 POST 请求
 */
export const upload = <T = unknown>(endpoint: string, formData: FormData, config?: RequestConfig) =>
  request<T>(endpoint, {
    ...config,
    method: 'POST',
    body: formData,
    headers: {
      // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
      ...config?.headers,
    },
  })

// 导出默认客户端
export const apiClient = {
  get,
  post,
  put,
  delete: del,
  upload,
  request,
}
