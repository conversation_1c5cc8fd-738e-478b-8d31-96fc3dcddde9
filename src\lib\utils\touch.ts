/**
 * 移动端触摸事件处理工具
 */

/**
 * 禁用元素的双击缩放
 * @param element - 要禁用双击缩放的元素
 */
export function disableDoubleTapZoom(element: HTMLElement): void {
  if (!element) return

  let lastTouchEnd = 0
  
  element.addEventListener('touchend', (event) => {
    const now = new Date().getTime()
    if (now - lastTouchEnd <= 300) {
      event.preventDefault()
    }
    lastTouchEnd = now
  }, { passive: false })
}

/**
 * 为整个文档禁用双击缩放
 */
export function disableDocumentDoubleTapZoom(): void {
  if (typeof document === 'undefined') return

  let lastTouchEnd = 0
  
  document.addEventListener('touchend', (event) => {
    const now = new Date().getTime()
    if (now - lastTouchEnd <= 300) {
      event.preventDefault()
    }
    lastTouchEnd = now
  }, { passive: false })
}

/**
 * 检测是否支持触摸
 */
export function isTouchDevice(): boolean {
  if (typeof window === 'undefined') return false
  
  return 'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    ((navigator as any).msMaxTouchPoints || 0) > 0
}

/**
 * 防止iOS Safari的橡皮筋效果（页面弹性滚动）
 */
export function preventBounceScroll(): void {
  if (typeof document === 'undefined') return

  document.addEventListener('touchmove', (event) => {
    // 检查是否是可滚动元素
    const target = event.target as HTMLElement
    const scrollableParent = findScrollableParent(target)
    
    if (!scrollableParent) {
      event.preventDefault()
      return
    }

    const { scrollTop, scrollHeight, clientHeight } = scrollableParent
    
    // 如果在顶部或底部，阻止默认行为
    if (
      (scrollTop === 0 && event.touches[0].clientY > event.touches[0].clientY) ||
      (scrollTop + clientHeight >= scrollHeight && event.touches[0].clientY < event.touches[0].clientY)
    ) {
      event.preventDefault()
    }
  }, { passive: false })
}

/**
 * 查找可滚动的父元素
 */
function findScrollableParent(element: HTMLElement): HTMLElement | null {
  if (!element) return null

  const { overflow, overflowY } = window.getComputedStyle(element)
  const isScrollable = overflow === 'auto' || overflow === 'scroll' || 
                      overflowY === 'auto' || overflowY === 'scroll'

  if (isScrollable && element.scrollHeight > element.clientHeight) {
    return element
  }

  return element.parentElement ? findScrollableParent(element.parentElement) : null
}

/**
 * 初始化移动端触摸优化
 * 在应用启动时调用
 */
export function initMobileTouchOptimization(): void {
  if (typeof window === 'undefined' || !isTouchDevice()) return

  // 禁用文档级别的双击缩放
  disableDocumentDoubleTapZoom()
  
  // 可选：防止橡皮筋效果（根据需要启用）
  // preventBounceScroll()
}
