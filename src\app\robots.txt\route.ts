import { NextResponse } from 'next/server'
import {
  shouldAllowSearchEngineIndexing,
  getWebsiteBaseUrl,
  getCurrentEnvironment,
  getEnvironmentDisplayName
} from '@/lib/seo/environment'

/**
 * 动态生成robots.txt文件
 * 根据环境变量控制搜索引擎爬虫访问权限
 */
export async function GET() {
  const allowIndexing = shouldAllowSearchEngineIndexing()
  const baseUrl = getWebsiteBaseUrl()
  const environment = getCurrentEnvironment()
  const envDisplayName = getEnvironmentDisplayName()

  let robotsContent: string

  if (allowIndexing) {
    // 生产环境：允许所有爬虫访问
    robotsContent = `User-agent: *
Allow: /

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml

# 常见爬虫特殊规则
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

User-agent: Baiduspider
Allow: /

# 禁止访问的路径
Disallow: /api/
Disallow: /_next/
Disallow: /admin/
Disallow: /dashboard/
Disallow: /*.json$

# 爬取延迟（秒）
Crawl-delay: 1

# 生成时间: ${new Date().toISOString()}
# 环境: ${envDisplayName}`
  } else {
    // 非生产环境：禁止所有爬虫访问
    robotsContent = `User-agent: *
Disallow: /

# ${envDisplayName} - 禁止搜索引擎索引
# Environment: ${environment}
# Base URL: ${baseUrl}
# Generated: ${new Date().toISOString()}`
  }

  return new NextResponse(robotsContent, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': allowIndexing
        ? 'public, max-age=86400, s-maxage=86400' // 生产环境缓存24小时
        : 'public, max-age=3600, s-maxage=3600',  // 非生产环境缓存1小时
      'X-Environment': environment,
      'X-Allow-Indexing': allowIndexing.toString(),
    },
  })
}
