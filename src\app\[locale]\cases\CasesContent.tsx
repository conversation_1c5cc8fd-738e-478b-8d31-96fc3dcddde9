'use client'

/**
 * 用户案例页面内容组件 - 客户端组件
 */
import { motion } from 'motion/react'
import Image from 'next/image'
import Link from 'next/link'
import type { StrapiHero, StrapiBlock } from '@/types/strapi'

// 定义媒体类型
interface Media {
  url: string
  alt?: string
}

// 定义按钮类型
interface Button {
  label: string
  url?: string
}

interface CasesContentProps {
  pageData: {
    title: string
    heroes: StrapiHero[]
    blocks: (StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] })[]
  } | null
}

// 优化的动画配置
const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.05,
    },
  },
}

const fadeInUp = {
  initial: { opacity: 0, y: 15 },
  animate: { opacity: 1, y: 0 },
}

// 区块组件
const BlockRenderer = ({
  block,
  index,
}: {
  block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] }
  index: number
}) => {
  const { __component, title, description, media, subtitle, buttons } = block

  // 判断是否为奇数块（从1开始计数）
  const isOdd = (index + 1) % 2 === 1

  // 根据不同的组件类型渲染不同的布局
  switch (__component) {
    case 'shared.block':
      return (
        <motion.section
          className="w-full bg-white"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, ease: 'easeOut' }}
          viewport={{ once: true, amount: 0.1 }}
        >
          {/* 内容区域 */}
          <div className="relative z-10 w-full">
            {/* PC端布局 */}
            <div className="hidden lg:block">
              <div className="mx-auto w-[76%] py-20">
                <motion.div
                  className={`flex gap-20 items-stretch ${isOdd ? 'flex-row' : 'flex-row-reverse'}`}
                  variants={staggerContainer}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true, amount: 0.1 }}
                >
                  {/* 图片区域 - 占一半宽度 */}
                  <div className="flex flex-1">
                    <motion.div className="w-full">
                      <Image
                        src={media?.url || ''}
                        width={500}
                        height={320}
                        alt={title || '客户病例'}
                        className="h-auto w-full object-cover"
                      />
                    </motion.div>
                  </div>

                  {/* 文字内容区域 - 占一半宽度 */}
                  <div className="flex flex-1 flex-col justify-between">
                    <motion.div variants={fadeInUp} transition={{ duration: 0.3, ease: 'easeOut' }}>
                      <div className="mb-20 text-sm leading-[28px] text-[#D2A76A]">{subtitle}</div>
                      <h2 className="mb-8 text-sm leading-[28px] font-[300] text-[#444]">
                        {title}
                      </h2>
                      <div className="text-justify text-[20px] leading-[36px] font-[500] text-black">
                        {description}
                      </div>
                    </motion.div>
                    {/* 按钮区域 */}
                    {buttons && buttons.length > 0 && (
                      <motion.div className="text-center" variants={fadeInUp}>
                        <div className="flex justify-start gap-4">
                          {buttons.map((button: Button, idx: number) => (
                            <Link
                              href={button.url || '#'}
                              key={idx}
                              className="bg-black text-sm  px-11 lg:px-12 py-[6px] lg:py-3 leading-[20px] lg:leading-[16px] text-white transition-colors hover:bg-gray-800 lg:px-12 lg:py-3"
                            >
                              {button.label}
                            </Link>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              </div>
            </div>

            {/* 移动端布局 - 保持原有的上下布局 */}
            <div className="block lg:hidden">
              <div className="w-full px-4 py-10">
                <motion.div
                  className="flex flex-col"
                  variants={staggerContainer}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true, amount: 0.1 }}
                >
                  <div>
                    <motion.div>
                      <div className="mb-3 text-sm leading-[28px] text-[#D2A76A]">{subtitle}</div>
                    </motion.div>
                    <motion.h2
                      className="mb-4 text-sm font-[300] text-[#444444]"
                      variants={fadeInUp}
                      transition={{ duration: 0.3, ease: 'easeOut' }}
                    >
                      {title}
                    </motion.h2>
                    <motion.div
                      className="text-justify text-[18px] leading-relaxed font-[500] text-black line-clamp-responsive"
                      variants={fadeInUp}
                      transition={{ duration: 0.3, delay: 0.1, ease: 'easeOut' }}
                    >
                      {description}
                    </motion.div>
                    {/* 按钮区域 */}
                    {buttons && buttons.length > 0 && (
                      <motion.div className="text-center" variants={fadeInUp}>
                        <div className="flex justify-start mb-[30px] mt-5 ">
                          {buttons.map((button: Button, idx: number) => (
                            <Link
                              href={button.url || '#'}
                              key={idx}
                              className="bg-black text-sm  px-11 lg:px-12 py-[6px] lg:py-3 leading-[20px] lg:leading-[16px] text-white transition-colors hover:bg-gray-800 lg:px-12 lg:py-3"
                            >
                              {button.label}
                            </Link>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </div>
                  <div>
                    <motion.div variants={fadeInUp} transition={{ duration: 0.3, ease: 'easeOut' }}>
                      <Image
                        src={media?.url || ''}
                        width={500}
                        height={320}
                        alt={title || '客户病例'}
                        className="h-auto w-full object-cover"
                      />
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.section>
      )

    default:
      return null
  }
}

// 主要的 CasesContent 组件
export default function CasesContent({ pageData }: CasesContentProps) {
  if (!pageData) {
    return (
      <div className="flex min-h-screen items-center justify-center pt-16 lg:pt-0">
        <p>页面数据加载中...</p>
      </div>
    )
  }

  return (
    <div className="bg-white pt-16 lg:bg-[#000] lg:pt-27">
      {/* Hero Banner */}
      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        {pageData.heroes.map((item: StrapiHero) => (
          <motion.div
            key={item.id}
            className="relative"
            initial={{ scale: 1.02 }}
            animate={{ scale: 1 }}
            transition={{ duration: 1.2, ease: 'easeOut' }}
          >
            {item?.media?.url && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
              >
                <Image
                  key={item.id}
                  src={item.media.url}
                  alt={item.title || ''}
                  width={1200}
                  height={800}
                  sizes="100vw"
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
            <motion.div
              className="absolute top-13 lg:top-10 left-7.5 z-10 w-full text-[#fff] md:top-[30%] lg:top-[32%] lg:left-[12%]"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="w-full lg:w-[28%]">
                <motion.h1
                  className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]"
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  {item.title}
                </motion.h1>
                <motion.div
                  className="hidden text-sm lg:block lg:text-justify font-[300] leading-[28px]"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  {item.description}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      {/* Blocks Content */}
      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="w-full">
          {pageData.blocks.map(
            (
              block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] },
              index: number
            ) => (
              <motion.div
                key={block.id || index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.05, ease: 'easeOut' }}
                viewport={{ once: true, amount: 0.05 }}
              >
                <BlockRenderer block={block} index={index} />
              </motion.div>
            )
          )}
        </div>
      </motion.div>
    </div>
  )
}
