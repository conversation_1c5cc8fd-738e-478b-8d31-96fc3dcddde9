/**
 * LAYOUT  Header  和Footer 的CMS API函数
 */

import type { <PERSON><PERSON>ead<PERSON>, IFooter, ISite } from '@/types/strapi'
import { fetchStrapiCollection } from './client'
import { API_ENDPOINTS, FOOTER_POPULATE } from './config'

/**
 * 获取Header数据
 */
export const getHeader = async (locale: string): Promise<IHeader | null> => {
  try {
    const response = await fetchStrapiCollection<IHeader>(
      API_ENDPOINTS.HEADER,
      locale,
      { 'populate': '*' }
    )
    // 返回第一个匹配的页面数据
    return response.data[0] || null
  } catch (error) {
    console.error('Failed to fetch About Us page:', error)
    return null
  }
}

/**
 * 获取Footer数据
 */
export const getFooter = async (locale: string): Promise<IFooter | null> => {
  try {
    const response = await fetchStrapiCollection<IFooter>(
      API_ENDPOINTS.FOOTER,
      locale,
      { populate: FOOTER_POPULATE }
    )
    // 返回第一个匹配的页面数据
    return response.data[0] || null
  } catch (error) {
    console.error('Failed to fetch Footer data:', error)
    return null
  }
}

/**
 * 获取Site基础数据
 */
export const getSite = async (locale: string): Promise<ISite | null> => {
  // console.log( buildSiteFilteredParams())
  try {
    const response = await fetchStrapiCollection<ISite>(
      API_ENDPOINTS.SITE,
      locale,
      { 'populate': '*' }
    )
    // 返回第一个匹配的页面数据
    return response.data[0] || null
  } catch (error) {
    console.error('Failed to fetch About Us page:', error)
    return null
  }
}