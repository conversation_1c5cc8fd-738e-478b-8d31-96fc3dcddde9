/**
 * Header Height Context for sharing header height across components
 */

'use client'

import { createContext, useContext, useState, useCallback, ReactNode } from 'react'

interface HeaderHeightContextType {
  headerHeight: number
  setHeaderHeight: (height: number) => void
  headerRef: (node: HTMLElement | null) => void
}

const HeaderHeightContext = createContext<HeaderHeightContextType | undefined>(undefined)

interface HeaderHeightProviderProps {
  children: ReactNode
}

export function HeaderHeightProvider({ children }: HeaderHeightProviderProps) {
  const [headerHeight, setHeaderHeight] = useState(0)
  const [cleanup, setCleanup] = useState<(() => void) | null>(null)

  // Ref callback to capture header element
  const headerRef = useCallback((node: HTMLElement | null) => {
    // Cleanup previous observers if any
    if (cleanup) {
      cleanup()
      setCleanup(null)
    }

    if (node) {
      // Initial measurement
      const height = node.getBoundingClientRect().height
      setHeaderHeight(height)

      // Set up ResizeObserver to watch for header size changes
      const resizeObserver = new ResizeObserver(() => {
        const newHeight = node.getBoundingClientRect().height
        setHeaderHeight(newHeight)
      })

      resizeObserver.observe(node)

      // Also listen for window resize as fallback
      const handleWindowResize = () => {
        const newHeight = node.getBoundingClientRect().height
        setHeaderHeight(newHeight)
      }

      window.addEventListener('resize', handleWindowResize)

      // Store cleanup function in state
      const cleanupFunction = () => {
        resizeObserver.disconnect()
        window.removeEventListener('resize', handleWindowResize)
      }

      setCleanup(() => cleanupFunction)
    } else {
      setHeaderHeight(0)
    }
  }, [cleanup])

  return (
    <HeaderHeightContext.Provider value={{ headerHeight, setHeaderHeight, headerRef }}>
      {children}
    </HeaderHeightContext.Provider>
  )
}

export function useHeaderHeight() {
  const context = useContext(HeaderHeightContext)
  if (context === undefined) {
    throw new Error('useHeaderHeight must be used within a HeaderHeightProvider')
  }
  return context
}
