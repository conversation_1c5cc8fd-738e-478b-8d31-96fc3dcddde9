/**
 * 通用类型定义文件
 */

// 导航菜单项类型
export interface NavItem {
  name: string
  href: string
  description?: string
  children?: NavItem[]
}

// 按钮变体类型
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost'
export type ButtonSize = 'sm' | 'md' | 'lg'

// 页面元数据类型
export interface PageMeta {
  title: string
  description: string
  keywords?: string
  image?: string
  url?: string
}

// API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 环境配置类型
export interface EnvironmentConfig {
  name: string
  displayName: string
  apiUrl: string
  appUrl: string
  features: {
    analytics: boolean
    debug: boolean
    errorReporting: boolean
  }
}

// 用户类型
export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'admin' | 'user' | 'guest'
  createdAt: string
  updatedAt: string
}

// 产品类型
export interface Product {
  id: string
  name: string
  description: string
  features: string[]
  price?: {
    monthly: number
    yearly: number
  }
  popular?: boolean
}

// 新闻/博客文章类型
export interface Article {
  id: string
  title: string
  excerpt: string
  content: string
  author: string
  publishedAt: string
  tags: string[]
  coverImage?: string
  slug: string
}
