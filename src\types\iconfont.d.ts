// Iconfont 相关类型定义

declare module '*.css' {
  const content: { [className: string]: string };
  export default content;
}

// 扩展 CSS 变量类型
declare module 'csstype' {
  interface Properties {
    '--font-iconfont'?: string;
  }
}

// 图标映射表（用于开发时的智能提示）
export interface IconMap {
  'yijianmeiyan1': '\e64d';
  'douyin': '\e648';
  'xiala2': '\e649';
  'xiaochengxu': '\e64a';
  'wancheng': '\e64b';
  'xiala': '\e64c';
  'zanting': '\e63a';
  'dianhua': '\e63b';
  'yachimeibai': '\e639';
  'weixin': '\e63c';
  'weizhi': '\e63d';
  'xiaohongshu': '\e638';
  'shijian': '\e63e';
  'shangchuanzhaopian': '\e640';
  'bofang': '\e641';
  'shouqi': '\e642';
  'guanbi': '\e643';
  'caidan': '\e644';
  'duibi': '\e645';
  'weibo': '\e646';
  'qiehuan': '\e647';
}

export type IconName = keyof IconMap;
