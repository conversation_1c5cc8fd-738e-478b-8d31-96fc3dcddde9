# 开发指南

> 📖 面向项目开发者的详细开发指南，包含环境配置、工作流程和最佳实践

## 🎯 文档目标

本文档为 Lumii Official Website 项目的开发者提供：
- 完整的开发环境配置指南
- 标准化的开发工作流程
- 代码规范和最佳实践
- 调试和测试方法
- 部署流程和注意事项

> 💡 **相关文档**: [技术架构](./ARCHITECTURE.md) | [项目概览](../README.md)

## 🛠️ 开发环境配置

### 系统要求
```bash
# 必需环境
Node.js >= 18.0.0
npm >= 9.0.0
Git >= 2.30.0

# 推荐环境
Node.js >= 20.0.0 (LTS)
npm >= 10.0.0
```

### 环境检查
```bash
# 检查版本
node --version    # 应该 >= 18.0.0
npm --version     # 应该 >= 9.0.0
git --version     # 应该 >= 2.30.0

# 检查全局包
npm list -g --depth=0
```

### 项目初始化
```bash
# 1. 克隆项目
git clone <repository-url>
cd lumii-official

# 2. 安装依赖
npm install

# 3. 环境变量配置
cp .env.example .env.local
# 编辑 .env.local 文件，配置必要的环境变量

# 4. 启动开发服务器
npm run dev
```

### IDE 配置推荐

#### VS Code 扩展
```json
// .vscode/extensions.json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag"
  ]
}
```

#### VS Code 设置
```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

## 🔧 开发工具详解

### 包管理器配置
```bash
# npm 配置优化
npm config set registry https://registry.npmjs.org/
npm config set save-exact true
npm config set fund false

# 查看当前配置
npm config list
```

### Git 配置
```bash
# 设置用户信息
git config user.name "Your Name"
git config user.email "<EMAIL>"

# 设置默认分支
git config init.defaultBranch main

# 设置自动换行
git config core.autocrlf input  # macOS/Linux
git config core.autocrlf true   # Windows
```

### 环境变量管理

项目使用简单直接的多环境配置管理，支持 5 个环境的独立配置。

```bash
# 本地开发（自动加载 .env.local）
npm run dev

# 构建不同环境
npm run build:dev      # 开发环境
npm run build:test     # 测试环境
npm run build:staging  # 预发环境
npm run build          # 生产环境
```

> 📖 详细的环境配置说明请参考 [环境配置文档](./ENVIRONMENT.md)

## 📋 开发工作流程

### 日常开发流程
```bash
# 1. 更新代码
git pull origin main

# 2. 创建功能分支
git checkout -b feature/new-feature

# 3. 安装依赖（如有更新）
npm install

# 4. 启动开发服务器
npm run dev

# 5. 开发过程中的检查
npm run lint        # 代码质量检查
npm run type-check  # 类型检查
npm run format      # 代码格式化

# 6. 提交代码
git add .
git commit -m "feat: add new feature"

# 7. 推送分支
git push origin feature/new-feature

# 8. 创建 Pull Request
```

### 开发命令详解
```bash
# 开发相关
npm run dev          # 启动开发服务器 (localhost:3000)
npm run dev:turbo    # 使用 Turbopack 启动 (更快)

# 构建相关
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run export       # 导出静态文件

# 代码质量
npm run lint         # ESLint 检查
npm run lint:fix     # 自动修复 ESLint 问题
npm run format       # Prettier 格式化
npm run format:check # 检查格式是否正确

# 类型检查
npm run type-check   # TypeScript 类型检查

# 工具命令
npm run clean        # 清理构建文件
npm run analyze      # 分析包大小
```

## 📝 代码规范和约定

### 文件命名规范
```bash
# 组件文件 - PascalCase
Button.tsx
UserProfile.tsx
ProductCard.tsx

# 页面文件 - kebab-case
page.tsx
layout.tsx
loading.tsx
error.tsx

# 工具文件 - camelCase
utils.ts
constants.ts
validations.ts

# 类型文件 - camelCase
types.ts
interfaces.ts
```

### 组件开发规范
```typescript
// 1. 导入顺序
import React from 'react'           // React 相关
import { motion } from 'motion/react' // 第三方库
import { cn } from '@/lib/utils'     // 内部工具
import Button from './Button'        // 内部组件

// 2. 接口定义
interface ComponentProps {
  title: string
  description?: string
  onClick?: () => void
}

// 3. 组件实现
export default function Component({
  title,
  description,
  onClick
}: ComponentProps) {
  // 组件逻辑
  return (
    <div className="p-4">
      <h2>{title}</h2>
      {description && <p>{description}</p>}
    </div>
  )
}
```

### Git 提交规范
```bash
# 提交类型
feat:     新功能
fix:      修复 bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建工具或辅助工具的变动

# 提交示例
git commit -m "feat: add user authentication"
git commit -m "fix: resolve header navigation issue"
git commit -m "docs: update API documentation"
```

### 代码注释规范
```typescript
/**
 * 用户认证组件
 * @param onLogin - 登录成功回调函数
 * @param onError - 错误处理回调函数
 * @returns 认证表单组件
 */
export function AuthForm({ onLogin, onError }: AuthFormProps) {
  // 实现逻辑...
}

// TODO: 添加记住密码功能
// FIXME: 修复移动端样式问题
// NOTE: 这里使用了特殊的算法优化性能
```

## 🧪 测试和调试

### 开发调试技巧
```typescript
// 1. 使用 console.log 调试
console.log('Debug info:', { variable, state })

// 2. 使用 React DevTools
// 安装浏览器扩展：React Developer Tools

// 3. 使用 Next.js 调试
// 在 VS Code 中配置调试
{
  "type": "node",
  "request": "launch",
  "name": "Next.js: debug server-side",
  "program": "${workspaceFolder}/node_modules/.bin/next",
  "args": ["dev"],
  "console": "integratedTerminal"
}

// 4. 网络请求调试
fetch('/api/data')
  .then(res => {
    console.log('Response status:', res.status)
    return res.json()
  })
  .then(data => console.log('Data:', data))
  .catch(err => console.error('Error:', err))
```

### 性能调试
```bash
# 1. 分析包大小
npm run analyze

# 2. 检查 Core Web Vitals
# 使用 Chrome DevTools Lighthouse

# 3. 监控运行时性能
# 使用 React Profiler
```

### 常见问题排查
```bash
# 1. 依赖问题
rm -rf node_modules package-lock.json
npm install

# 2. 缓存问题
npm run clean
rm -rf .next

# 3. 类型错误
npm run type-check

# 4. 样式问题
# 检查 Tailwind CSS 配置
# 确认类名拼写正确
```

## � 部署流程

### 本地构建测试
```bash
# 1. 构建项目
npm run build

# 2. 本地测试构建结果
npm run start

# 3. 检查构建产物
ls -la .next/

# 4. 分析包大小
npm run analyze
```

### Vercel 部署（推荐）
```bash
# 1. 安装 Vercel CLI
npm i -g vercel

# 2. 登录 Vercel
vercel login

# 3. 部署项目
vercel

# 4. 生产部署
vercel --prod
```

### 环境变量配置
```bash
# Vercel 环境变量设置
vercel env add NEXT_PUBLIC_API_URL production
vercel env add DATABASE_URL production

# 或通过 Vercel Dashboard 设置
```

### 自定义域名
```bash
# 添加域名
vercel domains add lumii.com

# 配置 DNS
# A 记录: @ -> 76.76.19.61
# CNAME 记录: www -> cname.vercel-dns.com
```

### 部署检查清单
- [ ] 环境变量配置正确
- [ ] 构建无错误和警告
- [ ] 性能指标达标
- [ ] SEO 元数据完整
- [ ] 响应式设计正常
- [ ] 动画效果流畅
- [ ] 错误页面正常显示

## � 开发资源和参考

### 官方文档
- [Next.js 文档](https://nextjs.org/docs) - Next.js 官方文档
- [React 文档](https://react.dev/) - React 官方文档
- [Tailwind CSS 文档](https://tailwindcss.com/docs) - Tailwind CSS 官方文档
- [Motion 文档](https://motion.dev/) - Motion 动画库文档
- [TypeScript 文档](https://www.typescriptlang.org/docs/) - TypeScript 官方文档

### 开发工具
- [React DevTools](https://react.dev/learn/react-developer-tools) - React 调试工具
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss) - VS Code 扩展
- [ES7+ React/Redux/React-Native snippets](https://marketplace.visualstudio.com/items?itemName=dsznajder.es7-react-js-snippets) - 代码片段

### 学习资源
- [Next.js Learn](https://nextjs.org/learn) - Next.js 官方教程
- [React 官方教程](https://react.dev/learn) - React 学习指南
- [Tailwind CSS 组件库](https://tailwindui.com/) - 官方组件库
- [Motion 示例](https://motion.dev/examples) - 动画示例

## 🤝 团队协作

### 分支管理策略
```bash
# 主分支
main              # 生产环境代码
develop           # 开发环境代码

# 功能分支
feature/user-auth # 用户认证功能
feature/dashboard # 仪表板功能

# 修复分支
hotfix/login-bug  # 紧急修复
```

### 代码审查流程
1. **创建 Pull Request** - 详细描述变更内容
2. **自动检查** - CI/CD 流水线检查
3. **人工审查** - 团队成员代码审查
4. **测试验证** - 功能测试和回归测试
5. **合并代码** - 审查通过后合并

### 沟通协作
- **日常沟通** - 使用 Slack/Teams 等工具
- **技术讨论** - GitHub Discussions 或 Issues
- **文档协作** - 使用 Notion/Confluence 等工具
- **设计协作** - 使用 Figma/Sketch 等工具

## 🎯 开发最佳实践总结

### 代码质量
- ✅ 使用 TypeScript 确保类型安全
- ✅ 遵循 ESLint 和 Prettier 规范
- ✅ 编写清晰的注释和文档
- ✅ 保持组件单一职责

### 性能优化
- ✅ 使用 Next.js Image 组件
- ✅ 实现代码分割和懒加载
- ✅ 优化 Core Web Vitals 指标
- ✅ 监控包大小和性能

### 用户体验
- ✅ 实现响应式设计
- ✅ 添加加载状态和错误处理
- ✅ 优化动画和交互效果
- ✅ 确保可访问性支持

---

## 📞 获取帮助

遇到问题时，可以通过以下方式获取帮助：

1. **查看文档** - [技术架构](./ARCHITECTURE.md) | [项目概览](../README.md)
2. **搜索 Issues** - 在项目 GitHub 仓库中搜索相关问题
3. **创建 Issue** - 描述问题并提供复现步骤
4. **团队讨论** - 在团队沟通群组中寻求帮助

---

*本开发指南会随着项目发展持续更新，确保为开发者提供最新的指导信息。*
