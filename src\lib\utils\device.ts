/**
 * 设备检测工具类
 */

/**
 * 检测是否为移动端设备
 * @returns {boolean} 是否为移动端
 */
export const isMobile = (): boolean => {
  // 服务端渲染时返回 false
  if (typeof window === 'undefined') {
    return false
  }

  // 检测用户代理字符串
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android',
    'webos',
    'iphone',
    'ipad',
    'ipod',
    'blackberry',
    'windows phone',
    'mobile',
    'opera mini'
  ]

  const isMobileUserAgent = mobileKeywords.some(keyword =>
    userAgent.includes(keyword)
  )

  // 检测屏幕宽度（移动端通常小于 768px）
  const isMobileScreen = window.innerWidth < 768

  // 检测触摸支持
  const hasTouchSupport = 'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    ((navigator as unknown as { msMaxTouchPoints?: number }).msMaxTouchPoints || 0) > 0

  // 综合判断：用户代理 + 屏幕宽度 + 触摸支持
  return isMobileUserAgent || (isMobileScreen && hasTouchSupport)
}

/**
 * 检测是否为平板设备
 * @returns {boolean} 是否为平板
 */
export const isTablet = (): boolean => {
  if (typeof window === 'undefined') {
    return false
  }

  const userAgent = navigator.userAgent.toLowerCase()
  const isIPad = userAgent.includes('ipad')
  const isAndroidTablet = userAgent.includes('android') && !userAgent.includes('mobile')
  const isLargeScreen = window.innerWidth >= 768 && window.innerWidth <= 1024

  return isIPad || isAndroidTablet || isLargeScreen
}

/**
 * 检测是否为桌面端设备
 * @returns {boolean} 是否为桌面端
 */
export const isDesktop = (): boolean => {
  return !isMobile() && !isTablet()
}

/**
 * 获取设备类型
 * @returns {'mobile' | 'tablet' | 'desktop'} 设备类型
 */
export const getDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {
  if (isMobile()) return 'mobile'
  if (isTablet()) return 'tablet'
  return 'desktop'
}
