/**
 * 微信小程序环境工具函数
 */

// 微信JSSDK错误响应类型
interface WxErrorResponse {
  errMsg?: string
  [key: string]: unknown
}

// 声明微信JSSDK类型
declare global {
  interface Window {
    wx?: {
      uploadImage: (options: {
        localId: string
        isShowProgressTips?: number
        success?: (res: { serverId: string }) => void
        fail?: (res: WxErrorResponse) => void
      }) => void
      downloadImage: (options: {
        serverId: string
        isShowProgressTips?: number
        success?: (res: { localId: string }) => void
        fail?: (res: WxErrorResponse) => void
      }) => void
      chooseImage: (options: {
        count?: number
        sizeType?: string[]
        sourceType?: string[]
        success?: (res: { localIds: string[] }) => void
        fail?: (res: WxErrorResponse) => void
      }) => void
    }
    __wxjs_environment?: string
    WeixinJSBridge?: {
      invoke?: (...args: unknown[]) => void
      [key: string]: unknown
    }
  }
}

/**
 * 检测是否在微信小程序环境中
 */
export function isMiniProgramEnvironment(): boolean {
  // 方法1: 检查 __wxjs_environment 变量
  if (typeof window !== 'undefined' && window.__wxjs_environment === 'miniprogram') {
    return true
  }
  
  // 方法2: 检查 userAgent 中是否包含 miniProgram
  if (typeof window !== 'undefined' && window.navigator) {
    const userAgent = window.navigator.userAgent
    if (userAgent.includes('miniProgram')) {
      return true
    }
  }
  
  return false
}

/**
 * 等待微信JSSDK准备就绪
 */
export function waitForWeixinJSBridge(): Promise<void> {
  return new Promise((resolve) => {
    if (typeof window === 'undefined') {
      resolve()
      return
    }

    function ready() {
      resolve()
    }

    if (!window.WeixinJSBridge || !window.WeixinJSBridge.invoke) {
      document.addEventListener('WeixinJSBridgeReady', ready, false)
    } else {
      ready()
    }
  })
}

/**
 * 在小程序环境中保存图片
 * @param file 要保存的图片文件
 * @param translations 翻译文本
 */
export async function saveImageInMiniProgram(
  file: File,
  translations?: { saveImageTip: string; close: string }
): Promise<void> {
  return new Promise((resolve, reject) => {
    // 将File转换为本地图片ID
    // 注意：这里需要先通过chooseImage或其他方式获取localId
    // 由于我们已经有了处理后的图片文件，我们需要先创建一个临时的图片URL
    const imageUrl = URL.createObjectURL(file)
    
    // 创建一个临时的img元素来获取图片数据
    const img = new Image()
    img.onload = () => {
      // 创建canvas来获取图片的base64数据
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) {
        URL.revokeObjectURL(imageUrl)
        reject(new Error('Canvas context not available'))
        return
      }
      
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0)
      
      // 获取base64数据
      const base64Data = canvas.toDataURL(file.type)
      
      // 清理临时URL
      URL.revokeObjectURL(imageUrl)
      
      // 在小程序环境中，我们需要使用不同的策略
      // 由于JSSDK的downloadImage需要serverId，我们需要先上传图片
      // 但这里我们采用一个简化的方案：提示用户长按保存
      showSaveImageTip(base64Data, translations)
      resolve()
    }
    
    img.onerror = () => {
      URL.revokeObjectURL(imageUrl)
      reject(new Error('Failed to load image'))
    }
    
    img.src = imageUrl
  })
}

/**
 * 显示保存图片提示
 * 在小程序环境中，由于限制，我们创建一个图片预览让用户长按保存
 */
function showSaveImageTip(base64Data: string, translations?: { saveImageTip: string; close: string }): void {
  // 默认翻译（中文）
  const defaultTranslations = {
    saveImageTip: '长按图片保存到相册',
    close: '关闭'
  }

  const t = translations || defaultTranslations
  // 创建一个模态框显示图片，提示用户长按保存
  const modal = document.createElement('div')
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 20px;
    box-sizing: border-box;
  `
  
  const tip = document.createElement('div')
  tip.style.cssText = `
    color: white;
    font-size: 16px;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.5;
  `
  tip.innerHTML = `
    <div>${t.saveImageTip}</div>
  `
  
  const img = document.createElement('img')
  img.style.cssText = `
    max-width: 90%;
    max-height: 70%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `
  img.src = base64Data
  
  const closeBtn = document.createElement('button')
  closeBtn.style.cssText = `
    margin-top: 20px;
    padding: 10px 20px;
    background: #fff;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
  `
  closeBtn.textContent = t.close
  
  closeBtn.onclick = () => {
    document.body.removeChild(modal)
  }
  
  modal.onclick = (e) => {
    if (e.target === modal) {
      document.body.removeChild(modal)
    }
  }
  
  modal.appendChild(tip)
  modal.appendChild(img)
  modal.appendChild(closeBtn)
  document.body.appendChild(modal)
}
