'use client'

import { motion } from 'motion/react'
import HeroImage from '@/components/ui/HeroImage'
import type { Dictionary } from '@/lib/i18n/types'
import type { StrapiHero } from '@/types/strapi'

interface AboutHeroProps {
  dictionary: Dictionary
  heroes?: StrapiHero[]
}

export default function AboutHero({ dictionary, heroes = [] }: AboutHeroProps) {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  return (
    <section className="relative overflow-hidden">
      {/* Hero 图片区域 - 放在最上面，占一行，无圆角 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="relative w-full h-screen"
      >
        {heroes.length > 0 ? (
          <HeroImage
            heroes={heroes}
            className="w-full h-full"
            priority={true}
          />
        ) : (
          // 如果没有 CMS 图片，显示默认的装饰性内容
          <div className="relative w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
            <div className="text-center">
              <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-700 mb-2">创新驱动</h3>
              <p className="text-gray-500">数字化未来</p>
            </div>
          </div>
        )}

        {/* 文字覆盖层 */}
        <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
          <motion.div
            initial="initial"
            animate="animate"
            variants={staggerContainer}
            className="text-center text-white px-4"
          >
            <motion.h1
              variants={fadeInUp}
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6"
            >
              <span className="bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                {dictionary.about.hero.title}
              </span>
            </motion.h1>

            <motion.p
              variants={fadeInUp}
              className="text-lg md:text-xl mb-8 leading-relaxed max-w-3xl mx-auto opacity-90"
            >
              {dictionary.about.hero.description}
            </motion.p>
          </motion.div>
        </div>
      </motion.div>
    </section>
  )
}
