/**
 * 文件上传组件 - 原子组件
 */

'use client'

import { useRef, useState, useCallback } from 'react'
import { motion } from 'motion/react'
import { Upload, Image as ImageIcon, AlertCircle } from 'lucide-react'
import { useTranslation } from '@/lib/i18n/client'
import { validateImageFile } from '@/lib/utils/validation'
import Button from '@/components/ui/Button'
import type { FileUploadProps } from '@/types/image-beautify'

export default function FileUpload({
  onFileSelect,
  accept = 'image/jpeg,image/png,image/webp',
  disabled = false,
  className = '',
  instructionText,
  buttonText
}: FileUploadProps) {
  const { t } = useTranslation()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragOver, setIsDragOver] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 处理文件选择
  const handleFileSelect = useCallback((file: File) => {
    setError(null)

    // 验证文件
    const validation = validateImageFile(file)
    if (!validation.valid) {
      const errorKey = `imageEditor.errors.${validation.error}`
      setError(t(errorKey, validation.error))
      return
    }

    onFileSelect(file)
  }, [onFileSelect, t])

  // 处理文件输入变化
  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }, [handleFileSelect])

  // 处理拖拽事件
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    if (!disabled) {
      setIsDragOver(true)
    }
  }, [disabled])

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(false)

    if (disabled) return

    const file = event.dataTransfer.files[0]
    if (file) {
      handleFileSelect(file)
    }
  }, [disabled, handleFileSelect])

  // 点击上传区域
  const handleClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }, [disabled])

  return (
    <div className={`relative ${className}`}>
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* 上传区域 */}
      <motion.div
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        whileHover={disabled ? {} : { scale: 1.02 }}
        whileTap={disabled ? {} : { scale: 0.98 }}
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
          transition-all duration-200 min-h-[200px] flex flex-col items-center justify-center
          ${isDragOver && !disabled
            ? 'border-blue-500 bg-blue-500/10'
            : 'border-gray-600 hover:border-gray-500'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          ${error ? 'border-red-500' : ''}
        `}
      >
        {/* 上传图标 */}
        <motion.div
          initial={{ scale: 1 }}
          animate={{ scale: isDragOver ? 1.1 : 1 }}
          transition={{ duration: 0.2 }}
          className="mb-4"
        >
          {isDragOver ? (
            <Upload className="w-12 h-12 text-blue-500" />
          ) : (
            <ImageIcon className="w-12 h-12 text-gray-400" />
          )}
        </motion.div>

        {/* 上传文本 */}
        <div className="space-y-4">
          {instructionText && (
            <p className="text-sm text-brand-gold">
              {instructionText}
            </p>
          )}
        </div>

        {/* 选择文件按钮 */}
        <Button
          variant="primary"
          disabled={disabled}
          className="mt-4"
        >
          {buttonText || t('imageEditor.upload.selectFile', '上传照片')}
        </Button>

        {/* 加载状态覆盖层 */}
        {disabled && (
          <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
            <div className="flex items-center space-x-2 text-white">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>{t('imageEditor.upload.uploading', '上传中...')}</span>
            </div>
          </div>
        )}
      </motion.div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-2 flex items-center space-x-2 text-red-400 text-sm"
        >
          <AlertCircle className="w-4 h-4" />
          <span>{error}</span>
        </motion.div>
      )}
    </div>
  )
}
