import { NextResponse } from 'next/server'
import { getWebsiteBaseUrl, shouldAllowSearchEngineIndexing } from '@/lib/seo/environment'

/**
 * 动态生成sitemap.xml文件
 */
export async function GET() {
  const baseUrl = getWebsiteBaseUrl()
  const allowIndexing = shouldAllowSearchEngineIndexing()
  
  // 获取当前时间作为lastmod
  const currentDate = new Date().toISOString().split('T')[0]
  
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${baseUrl}/zh-CN</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>${baseUrl}/en-US</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>${baseUrl}/zh-CN/about</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${baseUrl}/en-US/about</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
</urlset>`

  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': allowIndexing
        ? 'public, max-age=86400, s-maxage=86400' // 生产环境缓存24小时
        : 'public, max-age=3600, s-maxage=3600',  // 非生产环境缓存1小时
      'X-Allow-Indexing': allowIndexing.toString(),
    },
  })
}
