"use client"

import React, { useEffect, useRef } from "react"
import { motion } from "motion/react"

export type MagicLoadingOverlayProps = {
  className?: string
  canvasSize?: number // kept for API compatibility, no longer used in fullscreen mode
  starCount?: number
  dispersion?: number // 0..1, how far stars spread from center relative to canvas
  speed?: number // global animation speed multiplier
  sizeScale?: number // overall particle size multiplier
  title?: string
  subtitle?: string
}

// Draw a four-point magic star (diamond-like spikes) centered at (0,0)
function drawFourPointStar(
  ctx: CanvasRenderingContext2D,
  size: number,
  color: string,
  alpha: number,
  glow = true
) {
  ctx.save()
  ctx.globalAlpha = alpha
  if (glow) {
    ctx.shadowColor = color
    ctx.shadowBlur = size * 0.7
  } else {
    ctx.shadowBlur = 0
  }
  ctx.fillStyle = color

  // Star path: four spikes (up/right/down/left) with tapered ends
  const arm = size
  const thickness = Math.max(1, size * 0.18)
  ctx.beginPath()
  // Up spike
  ctx.moveTo(0, -arm)
  ctx.lineTo(thickness, -thickness)
  // Right spike
  ctx.lineTo(arm, 0)
  ctx.lineTo(thickness, thickness)
  // Down spike
  ctx.lineTo(0, arm)
  ctx.lineTo(-thickness, thickness)
  // Left spike
  ctx.lineTo(-arm, 0)
  ctx.lineTo(-thickness, -thickness)
  ctx.closePath()
  ctx.fill()

  // Core glow circle
  const r = thickness * 1.1
  const g = ctx.createRadialGradient(0, 0, 0, 0, 0, r * 2.0)
  g.addColorStop(0, `${color}`)
  g.addColorStop(1, "rgba(255,255,255,0)")
  ctx.fillStyle = g
  ctx.beginPath()
  ctx.arc(0, 0, r * 2.0, 0, Math.PI * 2)
  ctx.fill()

  ctx.restore()
}

export default function MagicLoadingOverlay({
  className = "",
  canvasSize = 320,
  starCount = 26,
  dispersion = 0.65,
  speed = 1,
  sizeScale = 1,
  title = "",
  subtitle = "",
}: MagicLoadingOverlayProps) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const rafRef = useRef<number | null>(null)

  type Star = {
    x: number
    y: number
    baseR: number // base arm length
    twinkleAmp: number
    twinkleSpd: number
    alphaBase: number
    phase: number
    vx: number
    vy: number
  }

  const stars = useRef<Star[]>([])

  const dpr = typeof window !== "undefined" ? Math.min(window.devicePixelRatio || 1, 2) : 1

  const setCanvasSize = () => {
    const c = canvasRef.current
    const container = containerRef.current
    if (!c || !container) return

    // Fullscreen canvas matching container
    const w = container.clientWidth
    const h = container.clientHeight
    c.style.width = `${w}px`
    c.style.height = `${h}px`
    c.width = Math.floor(w * dpr)
    c.height = Math.floor(h * dpr)
  }

  // Initialize stars - spread across fullscreen; dispersion controls clustering (1 = full spread)
  const initStars = (w: number, h: number) => {
    const count = Math.max(8, Math.min(200, starCount))
    const minDim = Math.min(w, h)

    const s: Star[] = []
    for (let i = 0; i < count; i++) {
      const x = w / 2 + (Math.random() - 0.5) * w * Math.min(1, Math.max(0.2, dispersion))
      const y = h / 2 + (Math.random() - 0.5) * h * Math.min(1, Math.max(0.2, dispersion))
      const baseR = (minDim * (0.008 + Math.random() * 0.02)) * Math.max(0.3, sizeScale)
      const twinkleAmp = baseR * (0.25 + Math.random() * 0.6)
      const twinkleSpd = (0.8 + Math.random() * 1.4) * 0.7 * speed
      const alphaBase = 0.35 + Math.random() * 0.65 // random brightness baseline
      const phase = Math.random() * Math.PI * 2
      const vx = (Math.random() * 2 - 1) * 0.12 * dpr
      const vy = (Math.random() * 2 - 1) * 0.12 * dpr
      s.push({ x, y, baseR, twinkleAmp, twinkleSpd, alphaBase, phase, vx, vy })
    }
    stars.current = s
  }

  // Resize and init on mount
  useEffect(() => {
    setCanvasSize()
    const c = canvasRef.current
    if (!c) return
    initStars(c.width, c.height)

    const onResize = () => {
      setCanvasSize()
      const c2 = canvasRef.current
      if (!c2) return
      initStars(c2.width, c2.height)
    }

    window.addEventListener("resize", onResize)
    return () => {
      window.removeEventListener("resize", onResize)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canvasSize, dispersion, starCount, speed])

  // Animation loop
  useEffect(() => {
    const c = canvasRef.current
    if (!c) return
    const ctx = c.getContext("2d")
    if (!ctx) return

    let last = performance.now()

    const render = (now: number) => {
      const dt = Math.min(64, now - last)
      last = now
      const w = c.width
      const h = c.height

      // Clear (no background & no blur per requirement)
      ctx.clearRect(0, 0, w, h)

      const t = now * 0.001 * speed
      ctx.save()
      ctx.globalCompositeOperation = "lighter"

      // Stars
      for (let i = 0; i < stars.current.length; i++) {
        const s = stars.current[i]
        // Twinkle and drift (no rotation)
        const twinkle = Math.sin(t * s.twinkleSpd + s.phase)
        const r = Math.max(0.5, s.baseR + twinkle * s.twinkleAmp)
        s.x += s.vx * (dt / 16.7)
        s.y += s.vy * (dt / 16.7)

        // Bounce on bounds with gentle pull back to center
        if (s.x < 0 || s.x > w) s.vx *= -1
        if (s.y < 0 || s.y > h) s.vy *= -1
        const cx = w / 2, cy = h / 2
        s.vx += ((cx - s.x) / w) * 0.003
        s.vy += ((cy - s.y) / h) * 0.003

        const alpha = Math.max(0, Math.min(1, s.alphaBase * (0.6 + 0.4 * (0.5 + twinkle * 0.5))))
        const color = `rgba(255,255,255,1)`

        ctx.save()
        ctx.translate(s.x, s.y)
        ctx.scale(dpr, dpr)
        drawFourPointStar(ctx, r, color, alpha, true)
        ctx.restore()
      }

      // Optional center pulse removed to keep background clean
      ctx.restore()

      rafRef.current = requestAnimationFrame(render)
    }

    rafRef.current = requestAnimationFrame(render)
    return () => {
      if (rafRef.current) cancelAnimationFrame(rafRef.current)
    }
  }, [speed, dpr])

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={["fixed inset-0 z-40", "flex items-center justify-center", className]
        .filter(Boolean)
        .join(" ")}
      ref={containerRef}
    >
      {/* Fullscreen transparent canvas */}
      <canvas ref={canvasRef} className="absolute inset-0 w-full h-full" />

      {(title || subtitle) && (
        <div className="relative z-10 text-center px-4 select-none pointer-events-none">
          {title && <h3 className="text-lg font-medium text-white drop-shadow mb-1">{title}</h3>}
          {subtitle && <p className="text-sm text-white/80 drop-shadow">{subtitle}</p>}
        </div>
      )}
    </motion.div>
  )
}

