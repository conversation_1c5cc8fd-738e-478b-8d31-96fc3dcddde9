import { cache } from 'react'
import { headers } from 'next/headers'
import { getDictionary } from './dictionaries'
import { extractLocaleFromPathname } from './utils'
import { i18n, type Locale } from './config'
import type { Dictionary } from './types'

/**
 * Get current locale from request headers (server-side)
 * This function is cached per request to avoid multiple header reads
 */
export const getCurrentLocale = cache(async (): Promise<Locale> => {
  const headersList = await headers()
  
  // Try to get locale from x-pathname header (set by middleware)
  const pathname = headersList.get('x-pathname') || '/'
  const localeFromPath = extractLocaleFromPathname(pathname)
  
  if (localeFromPath) {
    return localeFromPath
  }

  // Fallback to x-locale header (set by middleware)
  const localeFromHeader = headersList.get('x-locale') as Locale
  if (localeFromHeader && i18n.locales.includes(localeFromHeader)) {
    return localeFromHeader
  }

  // Final fallback
  return i18n.defaultLocale
})

/**
 * Get translation dictionary for current request (server-side)
 * This function is cached per request to avoid multiple dictionary loads
 */
export const getServerTranslationData = cache(async (): Promise<Dictionary> => {
  const locale = await getCurrentLocale()
  return getDictionary(locale)
})

/**
 * Server-side translation function
 * Usage: const { dictionary, locale, t } = await getServerTranslation()
 */
export const getServerTranslation = cache(async () => {
  const dictionary = await getServerTranslationData()
  const locale = await getCurrentLocale()

  return {
    dictionary,
    locale,
    t: (key: string, fallback?: string) => {
      const keys = key.split('.')
      let value: unknown = dictionary

      for (const k of keys) {
        if (
          value &&
          typeof value === 'object' &&
          k in (value as Record<string, unknown>)
        ) {
          value = (value as Record<string, unknown>)[k]
        } else {
          return fallback || key
        }
      }

      return typeof value === 'string' ? value : fallback || key
    }
  }
})
