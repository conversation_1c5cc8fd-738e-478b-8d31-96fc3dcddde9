/**
 * Shimmer效果 - 使用CSS Mask作用于透明PNG图片
 */

/* Shimmer动画关键帧 */
@keyframes shimmer-mask {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

/* 基础shimmer mask效果 */
.shimmer-mask {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.shimmer-mask::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.8) 50%,
    transparent 100%
  );
  animation: shimmer-mask 2s infinite linear;
  pointer-events: none;
  z-index: 1;
  /* 使用父元素的图片作为蒙版 */
  mask-image: inherit;
  mask-size: inherit;
  mask-position: inherit;
  mask-repeat: inherit;
}

/* 特殊效果：彩虹shimmer */
.shimmer-mask-rainbow {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.shimmer-mask-rainbow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 0, 150, 1) 15%,
    rgba(0, 204, 255, 1) 30%,
    rgba(255, 255, 255, 1) 50%,
    rgba(255, 255, 0, 1) 70%,
    rgba(255, 0, 150, 1) 85%,
    transparent 100%
  );
  animation: shimmer-mask 2s infinite linear;
  pointer-events: none;
  z-index: 1;
  mask-image: inherit;
  mask-size: inherit;
  mask-position: inherit;
  mask-repeat: inherit;
}
