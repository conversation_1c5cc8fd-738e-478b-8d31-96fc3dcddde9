/**
 * SEO环境检测工具
 */

export type Environment = 'prod' | 'pre' | 'test' | 'dev' | 'local'

/**
 * 获取当前环境类型
 */
export function getCurrentEnvironment(): Environment {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || ''
  
  // 生产环境检测
  if (apiUrl.includes('api.lumii.com.cn')) {
    return 'prod'
  }

  // 预发布环境
  if (apiUrl.includes('apipre.lumii.com.cn')) {
    return 'pre'
  }

  // 测试环境
  if (apiUrl.includes('apitest.lumii.com.cn')) {
    return 'test'
  }

  // 开发环境
  if (apiUrl.includes('apidev.lumii.com.cn')) {
    return 'dev'
  }
  
  // 本地环境
  return 'local'
}

/**
 * 检查是否为生产环境
 */
export function isProductionEnvironment(): boolean {
  return getCurrentEnvironment() === 'prod'
}

/**
 * 检查是否应该允许搜索引擎索引
 */
export function shouldAllowSearchEngineIndexing(): boolean {
  return isProductionEnvironment()
}

/**
 * 获取网站基础URL
 */
export function getWebsiteBaseUrl(): string {
  switch (getCurrentEnvironment()) {
    case 'prod':
      return 'https://www.lumii.com.cn'
    case 'pre':
      return 'https://pre.lumii.com.cn'
    case 'test':
      return 'https://test.lumii.com.cn'
    case 'dev':
      return 'https://dev.lumii.com.cn'
    case 'local':
    default:
      return 'http://localhost:3000'
  }
}

/**
 * 获取环境显示名称
 */
export function getEnvironmentDisplayName(): string {
  const env = getCurrentEnvironment()
  const envNames = {
    prod: '生产环境',
    pre: '预发布环境',
    test: '测试环境',
    dev: '开发环境',
    local: '本地环境'
  }

  return envNames[env] || '未知环境'
}
