/**
 * 图片美化页面主要区块组件 - 有机体组件
 */

'use client'

import { motion } from 'motion/react'
import ImageEditor from './ImageEditor'

export default function ImageBeautifySection() {

  return (
    <section className="min-h-screen bg-black text-white">
      {/* 图片编辑器区域 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        className="flex-1"
        style={{
          height: '100vh', // 占满整个视口高度
          minHeight: '600px'
        }}
      >
        <div className="h-full">
          <div className="max-w-7xl mx-auto h-full">
            <ImageEditor className="h-full" />
          </div>
        </div>
      </motion.div>
    </section>
  )
}
