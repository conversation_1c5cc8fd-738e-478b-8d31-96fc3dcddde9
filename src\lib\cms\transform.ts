/**
 * CMS数据转换函数
 * 将Strapi数据转换为Dictionary格式，以便与现有组件兼容
 */

import type { AboutUsPage } from '@/types/strapi'
import type { Dictionary } from '@/lib/i18n/types'

/**
 * 将Strapi About页面数据转换为Dictionary格式
 * 使用索引位置映射，避免语言关键词判断
 */
export function transformAboutPageToDictionary(
  cmsData: AboutUsPage,
  fallbackDictionary: Dictionary
): Dictionary {
  // 创建基础结构，从fallback复制非about部分
  const transformedDictionary: Dictionary = {
    ...fallbackDictionary,
    about: {
      title: cmsData.title,
      subtitle: fallbackDictionary.about.subtitle, // 保持原有subtitle
      hero: {
        title: cmsData.title,
        description: ''
      },
      mission: {
        title: '',
        description: ''
      },
      values: {
        title: '',
        innovation: {
          title: '',
          description: ''
        },
        excellence: {
          title: '',
          description: ''
        },
        collaboration: {
          title: '',
          description: ''
        }
      },
      team: {
        title: '',
        description: ''
      }
    }
  }

  // 使用索引位置映射blocks数据
  // 索引映射规则：0=hero, 1=mission, 2=values, 3=team
  cmsData.blocks.forEach((block, index) => {
    // 根据是否有子块来判断块类型
    const hasSubBlocks = block.blocks && block.blocks.length > 0

    if (!hasSubBlocks) {
      // 简单块（无子块）
      switch (index) {
        case 0:
          // Hero部分
          transformedDictionary.about.hero.title = block.title
          transformedDictionary.about.hero.description = block.description || ''
          break
        case 1:
          // Mission部分
          transformedDictionary.about.mission.title = block.title
          transformedDictionary.about.mission.description = block.description || ''
          break
        case 3:
          // Team部分
          transformedDictionary.about.team.title = block.title
          transformedDictionary.about.team.description = block.description || ''
          break
        default:
          // 其他位置的block暂不处理
          break
      }
    } else {
      // 复杂块（有子块）
      switch (index) {
        case 2:
          // Values部分（通常在索引2）
          transformedDictionary.about.values.title = block.title

          // 处理子块，使用索引位置映射：0=innovation, 1=excellence, 2=collaboration
          block.blocks?.forEach((subBlock, subIndex) => {
            switch (subIndex) {
              case 0:
                transformedDictionary.about.values.innovation.title = subBlock.title
                transformedDictionary.about.values.innovation.description = subBlock.description || ''
                break
              case 1:
                transformedDictionary.about.values.excellence.title = subBlock.title
                transformedDictionary.about.values.excellence.description = subBlock.description || ''
                break
              case 2:
                transformedDictionary.about.values.collaboration.title = subBlock.title
                transformedDictionary.about.values.collaboration.description = subBlock.description || ''
                break
              default:
                // 超出预期的子块数量，忽略
                break
            }
          })
          break
        case 1:
          // Mission部分（如果有子块但我们只取主标题）
          transformedDictionary.about.mission.title = block.title
          if (block.description) {
            transformedDictionary.about.mission.description = block.description
          }
          break
        case 3:
          // Team部分（如果有子块但我们只取主标题）
          transformedDictionary.about.team.title = block.title
          if (block.description) {
            transformedDictionary.about.team.description = block.description
          }
          break
        default:
          // 其他位置的复杂块暂不处理
          break
      }
    }
  })

  // 如果某些字段仍为空，使用fallback数据
  if (!transformedDictionary.about.hero.description) {
    transformedDictionary.about.hero.description = fallbackDictionary.about.hero.description
  }
  if (!transformedDictionary.about.mission.title) {
    transformedDictionary.about.mission.title = fallbackDictionary.about.mission.title
  }
  if (!transformedDictionary.about.mission.description) {
    transformedDictionary.about.mission.description = fallbackDictionary.about.mission.description
  }
  if (!transformedDictionary.about.values.title) {
    transformedDictionary.about.values.title = fallbackDictionary.about.values.title
  }
  if (!transformedDictionary.about.team.title) {
    transformedDictionary.about.team.title = fallbackDictionary.about.team.title
  }
  if (!transformedDictionary.about.team.description) {
    transformedDictionary.about.team.description = fallbackDictionary.about.team.description
  }

  // 确保values的子项有内容
  if (!transformedDictionary.about.values.innovation.title) {
    transformedDictionary.about.values.innovation = fallbackDictionary.about.values.innovation
  }
  if (!transformedDictionary.about.values.excellence.title) {
    transformedDictionary.about.values.excellence = fallbackDictionary.about.values.excellence
  }
  if (!transformedDictionary.about.values.collaboration.title) {
    transformedDictionary.about.values.collaboration = fallbackDictionary.about.values.collaboration
  }

  return transformedDictionary
}
